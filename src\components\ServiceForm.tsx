import React, { useState, <PERSON> } from 'react';
import { motion } from 'framer-motion';
import { CheckCircleIcon } from 'lucide-react';
const ServiceForm = () => {
  const [formData, setFormData] = useState({
    fullName: '',
    phone: '',
    email: '',
    serviceType: '',
    make: '',
    model: '',
    year: '',
    description: ''
  });
  const [formStatus, setFormStatus] = useState({
    submitted: false,
    submitting: false,
    error: null
  });
  const handleChange = e => {
    const {
      name,
      value
    } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  const handleSubmit = e => {
    e.preventDefault();
    setFormStatus({
      submitted: false,
      submitting: true,
      error: null
    });
    // Simulate form submission
    setTimeout(() => {
      setFormStatus({
        submitted: true,
        submitting: false,
        error: null
      });
      // In a real app, you would send this data to your Supabase backend
      console.log('Form submitted:', formData);
    }, 1500);
  };
  const serviceOptions = ['Transmission Rebuilding', 'Engine Repair & Rebuilding', 'Advanced Diagnostics', 'Brake Service', 'Electrical Service', 'Heating & Air Conditioning', 'Suspension & Steering', 'Exhaust Service', 'Key Programming', 'General Repair', 'Fleet Service', 'Other'];
  const formVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 20,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.4
      }
    }
  };
  const successVariants = {
    hidden: {
      scale: 0.8,
      opacity: 0
    },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 10
      }
    }
  };
  if (formStatus.submitted) {
    return <motion.div className="bg-white rounded-2xl p-8 shadow-xl text-center" variants={successVariants} initial="hidden" animate="visible">
        <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
          <CheckCircleIcon className="h-10 w-10 text-green-600" />
        </div>
        <h3 className="text-2xl font-bold text-gray-900 mb-2">
          Request Submitted Successfully!
        </h3>
        <p className="text-gray-600 mb-6">
          Thank you for your service request. Our team will contact you within
          24 hours with your estimate.
        </p>
        <button onClick={() => setFormStatus({
        submitted: false,
        submitting: false,
        error: null
      })} className="bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-3 px-8 rounded-lg transition-colors">
          Submit Another Request
        </button>
      </motion.div>;
  }
  return <motion.form onSubmit={handleSubmit} className="bg-white rounded-2xl p-8 shadow-xl" variants={formVariants} initial="hidden" animate="visible">
      <motion.h2 variants={itemVariants} className="text-2xl font-bold text-gray-900 mb-6">
        Book Appointment
      </motion.h2>
      <div className="space-y-6">
        <motion.div variants={itemVariants}>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Customer Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="fullName" className="block text-gray-700 font-medium mb-1">
                Full Name *
              </label>
              <input type="text" id="fullName" name="fullName" value={formData.fullName} onChange={handleChange} required className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" />
            </div>
            <div>
              <label htmlFor="phone" className="block text-gray-700 font-medium mb-1">
                Phone Number
              </label>
              <input type="tel" id="phone" name="phone" value={formData.phone} onChange={handleChange} className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" />
            </div>
          </div>
          <div className="mt-4">
            <label htmlFor="email" className="block text-gray-700 font-medium mb-1">
              Email Address *
            </label>
            <input type="email" id="email" name="email" value={formData.email} onChange={handleChange} required className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" />
          </div>
        </motion.div>
        <motion.div variants={itemVariants}>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Service Information
          </h3>
          <div>
            <label htmlFor="serviceType" className="block text-gray-700 font-medium mb-1">
              Service Type *
            </label>
            <select id="serviceType" name="serviceType" value={formData.serviceType} onChange={handleChange} required className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
              <option value="">Select service...</option>
              {serviceOptions.map((service, index) => <option key={index} value={service}>
                  {service}
                </option>)}
            </select>
          </div>
        </motion.div>
        <motion.div variants={itemVariants}>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Vehicle Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="make" className="block text-gray-700 font-medium mb-1">
                Make
              </label>
              <input type="text" id="make" name="make" value={formData.make} onChange={handleChange} className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" />
            </div>
            <div>
              <label htmlFor="model" className="block text-gray-700 font-medium mb-1">
                Model
              </label>
              <input type="text" id="model" name="model" value={formData.model} onChange={handleChange} className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" />
            </div>
            <div>
              <label htmlFor="year" className="block text-gray-700 font-medium mb-1">
                Year
              </label>
              <input type="text" id="year" name="year" value={formData.year} onChange={handleChange} className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" />
            </div>
          </div>
        </motion.div>
        <motion.div variants={itemVariants}>
          <label htmlFor="description" className="block text-gray-700 font-medium mb-1">
            Service Description *
          </label>
          <textarea id="description" name="description" value={formData.description} onChange={handleChange} required rows={4} className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" placeholder="Describe the service needed..."></textarea>
        </motion.div>
        <motion.div variants={itemVariants} className="pt-2">
          <button type="submit" disabled={formStatus.submitting} className={`w-full bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-3 px-6 rounded-lg transition-colors ${formStatus.submitting ? 'opacity-70 cursor-not-allowed' : ''}`}>
            {formStatus.submitting ? 'Submitting...' : 'Submit Quote Request'}
          </button>
          <p className="text-gray-500 text-sm mt-3 text-center">
            We'll contact you within 24 hours with your estimate
          </p>
        </motion.div>
      </div>
    </motion.form>;
};
export default ServiceForm;