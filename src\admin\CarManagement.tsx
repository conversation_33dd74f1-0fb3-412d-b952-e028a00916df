import React, { useState } from 'react';
import { PlusIcon, SearchIcon, TrashIcon, PencilIcon, CheckIcon, XIcon, UploadIcon, ChevronLeftIcon, ChevronRightIcon, FilterIcon, ArrowUpDownIcon, MenuIcon } from 'lucide-react';
import AdminSidebar from '../components/AdminSidebar';
const AdminCarManagement = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showAddCarForm, setShowAddCarForm] = useState(false);
  const [selectedCar, setSelectedCar] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState('dateAdded');
  const [sortDirection, setSortDirection] = useState('desc');
  // Sample car inventory data
  const cars = [{
    id: 1,
    title: '2019 Ford Mustang GT',
    price: '$32,500',
    image: 'https://images.unsplash.com/photo-1581650107963-3e8c1f0f0783?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '45,000 miles',
    year: 2019,
    make: 'Ford',
    model: 'Mustang GT',
    status: 'Available',
    featured: true,
    dateAdded: '2023-09-15',
    views: 245
  }, {
    id: 2,
    title: '2018 Toyota Camry XSE',
    price: '$22,800',
    image: 'https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '38,500 miles',
    year: 2018,
    make: 'Toyota',
    model: 'Camry XSE',
    status: 'Available',
    featured: false,
    dateAdded: '2023-09-20',
    views: 187
  }, {
    id: 3,
    title: '2020 Chevrolet Tahoe',
    price: '$42,999',
    image: 'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '29,000 miles',
    year: 2020,
    make: 'Chevrolet',
    model: 'Tahoe',
    status: 'Available',
    featured: true,
    dateAdded: '2023-08-28',
    views: 312
  }, {
    id: 4,
    title: '2021 Honda Accord Sport',
    price: '$28,750',
    image: 'https://images.unsplash.com/photo-1583121274602-3e2820c69888?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '12,300 miles',
    year: 2021,
    make: 'Honda',
    model: 'Accord Sport',
    status: 'Available',
    featured: false,
    dateAdded: '2023-10-05',
    views: 156
  }, {
    id: 5,
    title: '2017 Jeep Wrangler Unlimited',
    price: '$31,990',
    image: 'https://images.unsplash.com/photo-1563720223523-491ff04651de?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '52,000 miles',
    year: 2017,
    make: 'Jeep',
    model: 'Wrangler Unlimited',
    status: 'Pending Sale',
    featured: false,
    dateAdded: '2023-09-10',
    views: 278
  }];
  // Filter cars based on search query
  const filteredCars = cars.filter(car => car.title.toLowerCase().includes(searchQuery.toLowerCase()) || car.make.toLowerCase().includes(searchQuery.toLowerCase()) || car.model.toLowerCase().includes(searchQuery.toLowerCase()));
  // Sort cars
  const sortedCars = [...filteredCars].sort((a, b) => {
    if (sortField === 'price') {
      const aPrice = parseFloat(a.price.replace(/[^0-9.-]+/g, ''));
      const bPrice = parseFloat(b.price.replace(/[^0-9.-]+/g, ''));
      return sortDirection === 'asc' ? aPrice - bPrice : bPrice - aPrice;
    }
    if (sortField === 'year') {
      return sortDirection === 'asc' ? a.year - b.year : b.year - a.year;
    }
    if (sortField === 'dateAdded') {
      return sortDirection === 'asc' ? new Date(a.dateAdded).getTime() - new Date(b.dateAdded).getTime() : new Date(b.dateAdded).getTime() - new Date(a.dateAdded).getTime();
    }
    if (sortField === 'views') {
      return sortDirection === 'asc' ? a.views - b.views : b.views - a.views;
    }
    return 0;
  });
  // Pagination
  const itemsPerPage = 10;
  const totalPages = Math.ceil(sortedCars.length / itemsPerPage);
  const paginatedCars = sortedCars.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage);
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };
  const handleEditCar = (car: any) => {
    setSelectedCar(car);
    setShowAddCarForm(true);
  };
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  return <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
        <AdminSidebar activePage="cars" />
      </aside>

      {/* Mobile sidebar */}
      <div className={`fixed inset-0 bg-gray-800 bg-opacity-75 z-40 md:hidden transition-opacity duration-200 ${sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`} onClick={toggleSidebar}></div>

      <aside className={`fixed top-0 left-0 h-full bg-[#1e3a5f] text-white w-64 z-50 transform transition-transform duration-200 ease-in-out md:hidden ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="absolute top-4 right-4">
          <button className="text-white p-2" onClick={toggleSidebar}>
            <XIcon className="h-6 w-6" />
          </button>
        </div>
        <AdminSidebar activePage="cars" />
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top header */}
        <header className="bg-white shadow-sm z-10">
          <div className="px-4 py-4 flex justify-between items-center">
            <div className="flex items-center md:hidden">
              <button className="text-gray-600 p-2" onClick={toggleSidebar}>
                <MenuIcon className="h-6 w-6" />
              </button>
            </div>
            <h1 className="text-xl font-semibold text-gray-800">
              Car Inventory Management
            </h1>
            <button onClick={() => {
            setSelectedCar(null);
            setShowAddCarForm(true);
          }} className="bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-2 px-4 rounded-lg transition-colors flex items-center">
              <PlusIcon className="h-5 w-5 mr-2" />
              Add New Vehicle
            </button>
          </div>
        </header>

        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-gray-100 p-4">
          {showAddCarForm ? <AddEditCarForm car={selectedCar} onClose={() => setShowAddCarForm(false)} /> : <>
              <div className="bg-white rounded-xl shadow-md p-4 mb-6">
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <input type="text" placeholder="Search by make, model, or title..." value={searchQuery} onChange={e => setSearchQuery(e.target.value)} className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                      <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <span className="text-gray-600 mr-2">Status:</span>
                      <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="all">All</option>
                        <option value="available">Available</option>
                        <option value="pending">Pending Sale</option>
                        <option value="sold">Sold</option>
                      </select>
                    </div>
                    <button className="p-2 text-gray-600 hover:text-gray-800">
                      <FilterIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-md overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="min-w-full">
                    <thead>
                      <tr className="bg-gray-100 border-b border-gray-200">
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Vehicle
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <button onClick={() => handleSort('year')} className="flex items-center">
                            Year
                            <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                          </button>
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <button onClick={() => handleSort('price')} className="flex items-center">
                            Price
                            <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                          </button>
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Featured
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <button onClick={() => handleSort('dateAdded')} className="flex items-center">
                            Date Added
                            <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                          </button>
                        </th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          <button onClick={() => handleSort('views')} className="flex items-center">
                            Views
                            <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                          </button>
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      {paginatedCars.map(car => <tr key={car.id} className="hover:bg-gray-50">
                          <td className="px-4 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="h-10 w-16 flex-shrink-0 rounded overflow-hidden bg-gray-100">
                                <img src={car.image} alt={car.title} className="h-full w-full object-cover" />
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {car.title}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {car.mileage}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                            {car.year}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-700">
                            {car.price}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                              ${car.status === 'Available' ? 'bg-green-100 text-green-800' : car.status === 'Pending Sale' ? 'bg-yellow-100 text-yellow-800' : 'bg-blue-100 text-blue-800'}`}>
                              {car.status}
                            </span>
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            {car.featured ? <CheckIcon className="h-5 w-5 text-green-600" /> : <XIcon className="h-5 w-5 text-gray-400" />}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            {car.dateAdded}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                            {car.views}
                          </td>
                          <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex justify-end space-x-2">
                              <button onClick={() => handleEditCar(car)} className="text-blue-600 hover:text-blue-900">
                                <PencilIcon className="h-5 w-5" />
                              </button>
                              <button className="text-red-600 hover:text-red-900">
                                <TrashIcon className="h-5 w-5" />
                              </button>
                            </div>
                          </td>
                        </tr>)}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                {totalPages > 1 && <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200">
                    <div className="flex-1 flex justify-between items-center">
                      <p className="text-sm text-gray-700">
                        Showing{' '}
                        <span className="font-medium">
                          {(currentPage - 1) * itemsPerPage + 1}
                        </span>{' '}
                        to{' '}
                        <span className="font-medium">
                          {Math.min(currentPage * itemsPerPage, sortedCars.length)}
                        </span>{' '}
                        of{' '}
                        <span className="font-medium">{sortedCars.length}</span>{' '}
                        results
                      </p>
                      <div className="flex space-x-2">
                        <button onClick={() => setCurrentPage(currentPage - 1)} disabled={currentPage === 1} className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white ${currentPage === 1 ? 'text-gray-300' : 'text-gray-700 hover:bg-gray-50'}`}>
                          <ChevronLeftIcon className="h-4 w-4" />
                        </button>
                        <button onClick={() => setCurrentPage(currentPage + 1)} disabled={currentPage === totalPages} className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white ${currentPage === totalPages ? 'text-gray-300' : 'text-gray-700 hover:bg-gray-50'}`}>
                          <ChevronRightIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>}
              </div>
            </>}
        </main>
      </div>
    </div>;
};
const AddEditCarForm = ({
  car,
  onClose
}: {
  car: any;
  onClose: () => void;
}) => {
  const isEditing = !!car;
  const [formData, setFormData] = useState(car || {
    title: '',
    make: '',
    model: '',
    year: '',
    price: '',
    mileage: '',
    engine: '',
    transmission: '',
    exteriorColor: '',
    interiorColor: '',
    vin: '',
    stockNumber: '',
    description: '',
    status: 'Available',
    featured: false
  });
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const {
      name,
      value,
      type,
      checked
    } = e.target as HTMLInputElement;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would send data to your backend
    console.log('Form submitted:', formData);
    onClose();
  };
  return <div className="bg-white rounded-xl shadow-md p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-gray-900">
          {isEditing ? 'Edit Vehicle' : 'Add New Vehicle'}
        </h2>
        <button onClick={onClose} className="text-gray-400 hover:text-gray-500">
          <XIcon className="h-6 w-6" />
        </button>
      </div>
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-3">
            <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
              Vehicle Title *
            </label>
            <input type="text" id="title" name="title" value={formData.title} onChange={handleChange} required placeholder="e.g. 2019 Ford Mustang GT Premium" className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="make" className="block text-sm font-medium text-gray-700 mb-1">
              Make *
            </label>
            <input type="text" id="make" name="make" value={formData.make} onChange={handleChange} required className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="model" className="block text-sm font-medium text-gray-700 mb-1">
              Model *
            </label>
            <input type="text" id="model" name="model" value={formData.model} onChange={handleChange} required className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="year" className="block text-sm font-medium text-gray-700 mb-1">
              Year *
            </label>
            <input type="number" id="year" name="year" value={formData.year} onChange={handleChange} required min="1900" max={new Date().getFullYear() + 1} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
              Price *
            </label>
            <input type="text" id="price" name="price" value={formData.price} onChange={handleChange} required placeholder="e.g. $32,500" className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="mileage" className="block text-sm font-medium text-gray-700 mb-1">
              Mileage *
            </label>
            <input type="text" id="mileage" name="mileage" value={formData.mileage} onChange={handleChange} required placeholder="e.g. 45,000 miles" className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="engine" className="block text-sm font-medium text-gray-700 mb-1">
              Engine
            </label>
            <input type="text" id="engine" name="engine" value={formData.engine} onChange={handleChange} placeholder="e.g. 5.0L V8" className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="transmission" className="block text-sm font-medium text-gray-700 mb-1">
              Transmission
            </label>
            <input type="text" id="transmission" name="transmission" value={formData.transmission} onChange={handleChange} placeholder="e.g. 6-Speed Manual" className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="exteriorColor" className="block text-sm font-medium text-gray-700 mb-1">
              Exterior Color
            </label>
            <input type="text" id="exteriorColor" name="exteriorColor" value={formData.exteriorColor} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="interiorColor" className="block text-sm font-medium text-gray-700 mb-1">
              Interior Color
            </label>
            <input type="text" id="interiorColor" name="interiorColor" value={formData.interiorColor} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="vin" className="block text-sm font-medium text-gray-700 mb-1">
              VIN
            </label>
            <input type="text" id="vin" name="vin" value={formData.vin} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div>
            <label htmlFor="stockNumber" className="block text-sm font-medium text-gray-700 mb-1">
              Stock Number
            </label>
            <input type="text" id="stockNumber" name="stockNumber" value={formData.stockNumber} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
          </div>
          <div className="md:col-span-3">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea id="description" name="description" value={formData.description} onChange={handleChange} rows={4} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
          </div>
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select id="status" name="status" value={formData.status} onChange={handleChange} className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent">
              <option value="Available">Available</option>
              <option value="Pending Sale">Pending Sale</option>
              <option value="Sold">Sold</option>
            </select>
          </div>
          <div className="flex items-center">
            <input type="checkbox" id="featured" name="featured" checked={formData.featured} onChange={handleChange} className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" />
            <label htmlFor="featured" className="ml-2 block text-sm text-gray-900">
              Feature this vehicle on the homepage
            </label>
          </div>
        </div>
        <div className="border-t border-gray-200 pt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Vehicle Images
          </h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 mb-6">
            {/* Sample images - in a real app, these would come from the car object */}
            {isEditing && <>
                <div className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden">
                  <img src={car.image} alt="Vehicle" className="w-full h-full object-cover" />
                  <button className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full">
                    <XIcon className="h-4 w-4" />
                  </button>
                </div>
                {[1, 2, 3].map(i => <div key={i} className="relative aspect-video bg-gray-100 rounded-lg overflow-hidden">
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      Image {i + 1}
                    </div>
                    <button className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full">
                      <XIcon className="h-4 w-4" />
                    </button>
                  </div>)}
              </>}
            <div className="aspect-video bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors">
              <UploadIcon className="h-8 w-8 text-gray-400 mb-2" />
              <span className="text-sm text-gray-500">Add Image</span>
            </div>
          </div>
          <p className="text-sm text-gray-500">
            Upload up to 10 images. First image will be used as the main image.
          </p>
        </div>
        <div className="flex justify-end space-x-3 pt-6">
          <button type="button" onClick={onClose} className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
            Cancel
          </button>
          <button type="submit" className="px-6 py-2 bg-[#1e3a5f] hover:bg-blue-800 text-white rounded-lg transition-colors">
            {isEditing ? 'Update Vehicle' : 'Add Vehicle'}
          </button>
        </div>
      </form>
    </div>;
};
export default AdminCarManagement;