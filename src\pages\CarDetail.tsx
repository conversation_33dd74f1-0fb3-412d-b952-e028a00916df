import React, { useEffect, useState, Children } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ChevronLeftIcon, ChevronRightIcon, CheckIcon, CalendarIcon, GaugeIcon, FuelIcon, CarIcon, ClipboardCheckIcon, ShieldCheckIcon, StarIcon, PhoneIcon } from 'lucide-react';
const CarDetail = () => {
  const navigate = useNavigate();
  const {
    id
  } = useParams();
  const [activeImageIndex, setActiveImageIndex] = useState(0);
  const [inquiryFormVisible, setInquiryFormVisible] = useState(false);
  // Sample car data (in a real app, you would fetch this based on the ID)
  const car = {
    id: parseInt(id),
    title: '2019 Ford Mustang GT Premium',
    price: '$32,500',
    images: ['https://images.unsplash.com/photo-1581650107963-3e8c1f0f0783?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80', 'https://images.unsplash.com/photo-1584345604476-8ec5f82d718c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80', 'https://images.unsplash.com/photo-1494905998402-395d579af36f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80', 'https://images.unsplash.com/photo-1547245324-d777c6f05e80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80'],
    mileage: '45,000 miles',
    year: 2019,
    make: 'Ford',
    model: 'Mustang GT Premium',
    engine: '5.0L V8',
    transmission: '6-Speed Manual',
    drivetrain: 'Rear-Wheel Drive',
    exteriorColor: 'Race Red',
    interiorColor: 'Black Leather',
    fuelEconomy: '15 City / 24 Highway',
    fuelType: 'Gasoline',
    vin: '1FA6P8CF3K5187429',
    stockNumber: 'P12345',
    featured: true,
    category: 'sports',
    description: 'This 2019 Ford Mustang GT Premium is a true American muscle car with exhilarating performance and head-turning style. Featuring the powerful 5.0L V8 engine paired with a smooth 6-speed manual transmission, this Mustang delivers an authentic driving experience that enthusiasts crave. The Race Red exterior is in excellent condition, complemented by the premium black leather interior that offers both comfort and style. With only 45,000 miles, this Mustang has been well-maintained and is ready for its new owner.',
    features: ['5.0L V8 Engine', '6-Speed Manual Transmission', 'Premium Leather Seats', 'SYNC 3 Infotainment System', 'Apple CarPlay & Android Auto', 'Heated & Cooled Front Seats', 'Premium Audio System', 'Rear View Camera', 'Keyless Entry & Push Button Start', 'LED Headlights', 'Performance Exhaust System', '19-inch Premium Wheels', 'Blind Spot Monitoring System', 'Dual-Zone Climate Control'],
    warranty: '3-Month / 3,000 Mile Limited Warranty',
    carfaxReport: true,
    serviceHistory: true,
    oneOwner: true
  };
  // Sample similar cars
  const similarCars = [{
    id: 2,
    title: '2020 Chevrolet Camaro SS',
    price: '$35,900',
    image: 'https://images.unsplash.com/photo-1552519507-da3b142c6e3d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '32,000 miles',
    year: 2020
  }, {
    id: 3,
    title: '2018 Dodge Challenger R/T',
    price: '$29,500',
    image: 'https://images.unsplash.com/photo-1596219480320-7d3a7539589e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1374&q=80',
    mileage: '48,500 miles',
    year: 2018
  }, {
    id: 4,
    title: '2021 Ford Mustang EcoBoost',
    price: '$28,750',
    image: 'https://images.unsplash.com/photo-1603553329474-99f95f35394f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '18,300 miles',
    year: 2021
  }];
  const nextImage = () => {
    setActiveImageIndex(prevIndex => prevIndex === car.images.length - 1 ? 0 : prevIndex + 1);
  };
  const prevImage = () => {
    setActiveImageIndex(prevIndex => prevIndex === 0 ? car.images.length - 1 : prevIndex - 1);
  };
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 20,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  // Scroll to the inquiry form when button is clicked
  const handleInquireClick = () => {
    setInquiryFormVisible(true);
    setTimeout(() => {
      const element = document.getElementById('inquiry-form');
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }, 100);
  };
  return <main className="w-full pt-20 pb-16 bg-slate-100">
      <div className="container mx-auto px-4 md:px-8">
        {/* Breadcrumb */}
        <div className="py-4">
          <div className="flex items-center text-sm">
            <Link to="/" className="text-gray-500 hover:text-[#1e3a5f]">
              Home
            </Link>
            <ChevronRightIcon className="h-4 w-4 mx-2 text-gray-400" />
            <Link to="/cars" className="text-gray-500 hover:text-[#1e3a5f]">
              Cars
            </Link>
            <ChevronRightIcon className="h-4 w-4 mx-2 text-gray-400" />
            <span className="text-[#1e3a5f] font-medium">{car.title}</span>
          </div>
        </div>

        <motion.div variants={containerVariants} initial="hidden" animate="visible">
          <motion.div variants={itemVariants} className="flex flex-col lg:flex-row gap-8 mb-12">
            {/* Image Gallery */}
            <div className="lg:w-3/5">
              <div className="bg-white rounded-2xl overflow-hidden shadow-xl">
                <div className="relative h-[300px] sm:h-[400px] md:h-[500px]">
                  {car.images.map((image, index) => <motion.div key={index} initial={{
                  opacity: 0
                }} animate={{
                  opacity: activeImageIndex === index ? 1 : 0
                }} transition={{
                  duration: 0.5
                }} className={`absolute inset-0 ${activeImageIndex === index ? 'block' : 'hidden'}`}>
                      <img src={image} alt={`${car.title} - Image ${index + 1}`} className="w-full h-full object-cover" />
                    </motion.div>)}
                  <button onClick={prevImage} className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors">
                    <ChevronLeftIcon className="h-6 w-6" />
                  </button>
                  <button onClick={nextImage} className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 transition-colors">
                    <ChevronRightIcon className="h-6 w-6" />
                  </button>
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                    {car.images.map((_, index) => <button key={index} onClick={() => setActiveImageIndex(index)} className={`h-2 w-2 rounded-full transition-colors ${activeImageIndex === index ? 'bg-white' : 'bg-white/50'}`}></button>)}
                  </div>
                </div>
                <div className="p-4 grid grid-cols-4 gap-2">
                  {car.images.map((image, index) => <button key={index} onClick={() => setActiveImageIndex(index)} className={`h-16 sm:h-20 rounded-lg overflow-hidden border-2 transition-colors ${activeImageIndex === index ? 'border-blue-600' : 'border-transparent'}`}>
                      <img src={image} alt={`${car.title} - Thumbnail ${index + 1}`} className="w-full h-full object-cover" />
                    </button>)}
                </div>
              </div>
            </div>

            {/* Car Details */}
            <div className="lg:w-2/5">
              <div className="bg-white rounded-2xl shadow-xl p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">
                      {car.title}
                    </h1>
                    <div className="flex items-center mt-1">
                      <div className="flex">
                        <StarIcon className="h-5 w-5 text-yellow-500" />
                        <StarIcon className="h-5 w-5 text-yellow-500" />
                        <StarIcon className="h-5 w-5 text-yellow-500" />
                        <StarIcon className="h-5 w-5 text-yellow-500" />
                        <StarIcon className="h-5 w-5 text-yellow-500" />
                      </div>
                      <span className="text-sm text-blue-600 ml-2">
                        12 Reviews
                      </span>
                    </div>
                  </div>
                  {car.featured && <span className="bg-yellow-500 text-white text-sm font-bold px-3 py-1 rounded-full">
                      Featured
                    </span>}
                </div>
                <div className="flex items-baseline mb-6">
                  <span className="text-3xl font-bold text-blue-700">
                    {car.price}
                  </span>
                  <span className="text-gray-500 ml-2">
                    + taxes & licensing
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="flex items-center">
                    <CalendarIcon className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <p className="text-sm text-gray-500">Year</p>
                      <p className="font-medium">{car.year}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <GaugeIcon className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <p className="text-sm text-gray-500">Mileage</p>
                      <p className="font-medium">{car.mileage}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <CarIcon className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <p className="text-sm text-gray-500">Transmission</p>
                      <p className="font-medium">{car.transmission}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <FuelIcon className="h-5 w-5 text-blue-600 mr-2" />
                    <div>
                      <p className="text-sm text-gray-500">Fuel Type</p>
                      <p className="font-medium">{car.fuelType}</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4 mb-8">
                  {car.oneOwner && <div className="flex items-center text-green-700">
                      <CheckIcon className="h-5 w-5 mr-2" />
                      <span>One Owner Vehicle</span>
                    </div>}
                  {car.carfaxReport && <div className="flex items-center text-green-700">
                      <CheckIcon className="h-5 w-5 mr-2" />
                      <span>Clean CARFAX Report</span>
                    </div>}
                  {car.serviceHistory && <div className="flex items-center text-green-700">
                      <CheckIcon className="h-5 w-5 mr-2" />
                      <span>Full Service History</span>
                    </div>}
                  <div className="flex items-center text-green-700">
                    <CheckIcon className="h-5 w-5 mr-2" />
                    <span>{car.warranty}</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <button onClick={handleInquireClick} className="w-full bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-3 rounded-lg transition-colors flex items-center justify-center">
                    Inquire About This Vehicle
                  </button>
                  <a href="tel:+16714838335" className="w-full border-2 border-[#1e3a5f] text-[#1e3a5f] hover:bg-[#1e3a5f] hover:text-white font-semibold py-3 rounded-lg transition-colors flex items-center justify-center">
                    <PhoneIcon className="h-5 w-5 mr-2" />
                    Call About This Vehicle
                  </a>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Vehicle Description and Features */}
          <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            {/* Description */}
            <div className="lg:col-span-2 bg-white rounded-2xl shadow-xl p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Vehicle Description
              </h2>
              <p className="text-gray-700 mb-6 leading-relaxed">
                {car.description}
              </p>
              <h3 className="text-xl font-bold text-gray-900 mb-3">
                Key Specifications
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Make</span>
                  <span className="font-medium">{car.make}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Model</span>
                  <span className="font-medium">{car.model}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Year</span>
                  <span className="font-medium">{car.year}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Mileage</span>
                  <span className="font-medium">{car.mileage}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Engine</span>
                  <span className="font-medium">{car.engine}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Transmission</span>
                  <span className="font-medium">{car.transmission}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Drivetrain</span>
                  <span className="font-medium">{car.drivetrain}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Exterior Color</span>
                  <span className="font-medium">{car.exteriorColor}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Interior Color</span>
                  <span className="font-medium">{car.interiorColor}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Fuel Economy</span>
                  <span className="font-medium">{car.fuelEconomy}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">VIN</span>
                  <span className="font-medium">{car.vin}</span>
                </div>
                <div className="flex justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-600">Stock #</span>
                  <span className="font-medium">{car.stockNumber}</span>
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="bg-white rounded-2xl shadow-xl p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Features & Options
              </h2>
              <ul className="space-y-3">
                {car.features.map((feature, index) => <li key={index} className="flex items-start">
                    <CheckIcon className="h-5 w-5 text-green-600 mr-2 mt-0.5" />
                    <span>{feature}</span>
                  </li>)}
              </ul>
            </div>
          </motion.div>

          {/* Vehicle History and Warranty */}
          <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            <div className="bg-white rounded-2xl shadow-xl p-6">
              <div className="flex items-center mb-4">
                <ClipboardCheckIcon className="h-8 w-8 text-blue-600 mr-3" />
                <h2 className="text-2xl font-bold text-gray-900">
                  Vehicle History
                </h2>
              </div>
              <p className="text-gray-700 mb-6">
                This vehicle comes with a clean history report and has been
                thoroughly inspected by our certified technicians.
              </p>
              <div className="space-y-4">
                <div className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-600 mr-2" />
                  <span className="text-gray-700">No Accidents Reported</span>
                </div>
                <div className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-600 mr-2" />
                  <span className="text-gray-700">No Damage Reported</span>
                </div>
                <div className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-600 mr-2" />
                  <span className="text-gray-700">Regular Maintenance</span>
                </div>
                <div className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-600 mr-2" />
                  <span className="text-gray-700">Personal Use Only</span>
                </div>
              </div>
              <div className="mt-6">
                <button className="text-blue-600 font-medium hover:text-blue-800 transition-colors flex items-center">
                  View Full CARFAX Report
                  <ChevronRightIcon className="h-5 w-5 ml-1" />
                </button>
              </div>
            </div>

            <div className="bg-white rounded-2xl shadow-xl p-6">
              <div className="flex items-center mb-4">
                <ShieldCheckIcon className="h-8 w-8 text-blue-600 mr-3" />
                <h2 className="text-2xl font-bold text-gray-900">
                  Warranty Information
                </h2>
              </div>
              <p className="text-gray-700 mb-6">
                This vehicle comes with our standard 3-month/3,000-mile limited
                warranty. Extended warranty options are available for additional
                peace of mind.
              </p>
              <div className="space-y-4">
                <div className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-600 mr-2" />
                  <span className="text-gray-700">
                    3-Month/3,000-Mile Limited Warranty
                  </span>
                </div>
                <div className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-600 mr-2" />
                  <span className="text-gray-700">
                    24/7 Roadside Assistance
                  </span>
                </div>
                <div className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-600 mr-2" />
                  <span className="text-gray-700">
                    Complimentary First Service
                  </span>
                </div>
                <div className="flex items-center">
                  <CheckIcon className="h-5 w-5 text-green-600 mr-2" />
                  <span className="text-gray-700">
                    Extended Warranty Options Available
                  </span>
                </div>
              </div>
              <div className="mt-6">
                <button className="text-blue-600 font-medium hover:text-blue-800 transition-colors flex items-center">
                  Learn About Extended Warranty Options
                  <ChevronRightIcon className="h-5 w-5 ml-1" />
                </button>
              </div>
            </div>
          </motion.div>

          {/* Inquiry Form */}
          {inquiryFormVisible && <motion.div id="inquiry-form" variants={itemVariants} className="bg-white rounded-2xl shadow-xl p-6 mb-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                Inquire About This Vehicle
              </h2>
              <p className="text-gray-700 mb-6">
                Fill out the form below and our team will contact you shortly
                about this {car.year} {car.make} {car.model}.
              </p>
              <form className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-gray-700 font-medium mb-2">
                      Full Name *
                    </label>
                    <input type="text" id="name" required className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-gray-700 font-medium mb-2">
                      Email Address *
                    </label>
                    <input type="email" id="email" required className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" />
                  </div>
                  <div>
                    <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">
                      Phone Number *
                    </label>
                    <input type="tel" id="phone" required className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" />
                  </div>
                  <div>
                    <label htmlFor="contact-preference" className="block text-gray-700 font-medium mb-2">
                      Preferred Contact Method
                    </label>
                    <select id="contact-preference" className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                      <option value="email">Email</option>
                      <option value="phone">Phone</option>
                      <option value="text">Text Message</option>
                    </select>
                  </div>
                </div>
                <div>
                  <label htmlFor="message" className="block text-gray-700 font-medium mb-2">
                    Message
                  </label>
                  <textarea id="message" rows={4} placeholder="I'm interested in this vehicle and would like more information..." className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all" defaultValue={`I'm interested in the ${car.year} ${car.make} ${car.model} (Stock #${car.stockNumber}).`}></textarea>
                </div>
                <div className="flex items-start">
                  <input type="checkbox" id="consent" className="mt-1 mr-2" />
                  <label htmlFor="consent" className="text-sm text-gray-600">
                    I consent to receive email, text messages, and phone calls
                    with offers, vehicle information, and other marketing
                    communications from ADJ Automotive.
                  </label>
                </div>
                <div className="flex flex-wrap gap-4">
                  <button type="submit" className="bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-3 px-8 rounded-lg transition-colors">
                    Submit Inquiry
                  </button>
                  <button type="button" onClick={() => setInquiryFormVisible(false)} className="border-2 border-gray-300 text-gray-700 hover:bg-gray-100 font-semibold py-3 px-8 rounded-lg transition-colors">
                    Cancel
                  </button>
                </div>
              </form>
            </motion.div>}

          {/* Similar Vehicles */}
          <motion.div variants={itemVariants}>
            <h2 className="text-2xl font-bold text-gray-900 mb-6">
              Similar Vehicles
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {similarCars.map(similarCar => <div key={similarCar.id} className="bg-white rounded-2xl overflow-hidden shadow-xl">
                  <div className="h-48 overflow-hidden">
                    <img src={similarCar.image} alt={similarCar.title} className="w-full h-full object-cover transition-transform duration-500 hover:scale-110" />
                  </div>
                  <div className="p-6">
                    <h3 className="text-lg font-bold text-gray-900 mb-1">
                      {similarCar.title}
                    </h3>
                    <div className="flex justify-between items-center mb-4">
                      <span className="text-xl font-bold text-blue-700">
                        {similarCar.price}
                      </span>
                      <span className="text-gray-600">
                        {similarCar.mileage}
                      </span>
                    </div>
                    <Link to={`/cars/${similarCar.id}`} className="inline-block bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-2 px-6 rounded-lg transition-colors w-full text-center">
                      View Details
                    </Link>
                  </div>
                </div>)}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </main>;
};
export default CarDetail;