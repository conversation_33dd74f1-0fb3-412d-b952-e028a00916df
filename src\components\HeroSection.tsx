import React, { useEffect, useRef, useState } from 'react';
import { motion, useAnimation, useInView, AnimatePresence } from 'framer-motion';
import { ArrowRightIcon, ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);

  const slides = [
    {
      id: 1,
      backgroundImage: "url('https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80')",
      badge: "TRUSTED AUTOMOTIVE EXPERTS",
      title: "Dealership Quality Repair at an Affordable Price",
      description: "Specializing in transmission rebuilding, engine repair, and advanced diagnostics with over 38 years of combined experience.",
      primaryButton: "Book Appointment",
      secondaryButton: "View Cars for Sale"
    },
    {
      id: 2,
      backgroundImage: "url('https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80')",
      badge: "EXPERT TRANSMISSION SERVICE",
      title: "Professional Transmission Rebuilding & Repair",
      description: "Complete transmission overhauls with 1-year labor warranty. From diagnostics to full rebuilds, we handle all transmission needs.",
      primaryButton: "Transmission Services",
      secondaryButton: "Get Free Quote"
    },
    {
      id: 3,
      backgroundImage: "url('https://images.unsplash.com/photo-1601362840469-51e4d8d58785?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80')",
      badge: "ADVANCED DIAGNOSTICS",
      title: "State-of-the-Art Diagnostic Equipment",
      description: "Using the latest Autel MaxiSys Ultra scan tool to diagnose and resolve even the most complex vehicle issues quickly and accurately.",
      primaryButton: "Diagnostic Services",
      secondaryButton: "Call (*************"
    }
  ];

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  // Auto-play functionality
  useEffect(() => {
    if (isAutoPlaying) {
      autoPlayRef.current = setInterval(() => {
        nextSlide();
      }, 5000); // Change slide every 5 seconds
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [isAutoPlaying, currentSlide]);

  // Pause auto-play on hover
  const handleMouseEnter = () => setIsAutoPlaying(false);
  const handleMouseLeave = () => setIsAutoPlaying(true);

  const slideVariants = {
    enter: {
      opacity: 0,
      scale: 1.1
    },
    center: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: 'easeOut'
      }
    },
    exit: {
      opacity: 0,
      scale: 0.9,
      transition: {
        duration: 0.5,
        ease: 'easeIn'
      }
    }
  };

  const contentVariants = {
    hidden: {
      opacity: 0,
      y: 30
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        delay: 0.3,
        ease: 'easeOut'
      }
    }
  };

  return (
    <section 
      className="relative w-full min-h-screen overflow-hidden" 
      style={{ height: '100vh' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Carousel Slides */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          className="absolute inset-0 z-0"
          variants={slideVariants}
          initial="enter"
          animate="center"
          exit="exit"
          style={{
            backgroundImage: slides[currentSlide].backgroundImage,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            width: '100%',
            height: '100%'
          }}
        >
          {/* Overlay for better text readability */}
          <div 
            className="absolute inset-0 z-10" 
            style={{
              background: 'linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.7))'
            }}
          ></div>
        </motion.div>
      </AnimatePresence>

      {/* Content */}
      <div className="absolute inset-0 z-20">
        <div className="container mx-auto h-full flex items-center px-4 md:px-8">
          <AnimatePresence mode="wait">
            <motion.div 
              key={currentSlide}
              className="max-w-3xl" 
              variants={contentVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              <motion.div className="mb-2">
                <span className="inline-block bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold mb-3">
                  {slides[currentSlide].badge}
                </span>
              </motion.div>
              <motion.h1 
                className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight"
              >
                {slides[currentSlide].title}
              </motion.h1>
              <motion.p 
                className="text-lg md:text-xl text-gray-200 mb-8"
              >
                {slides[currentSlide].description}
              </motion.p>
              <motion.div className="flex flex-wrap gap-4">
                <motion.button 
                  className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-full font-semibold text-lg flex items-center" 
                  whileHover={{
                    scale: 1.05
                  }} 
                  whileTap={{
                    scale: 0.95
                  }} 
                  transition={{
                    type: 'spring',
                    stiffness: 400,
                    damping: 17
                  }}
                >
                  {slides[currentSlide].primaryButton} <ArrowRightIcon className="ml-2 h-5 w-5" />
                </motion.button>
                <motion.button 
                  className="bg-white hover:bg-gray-100 text-[#1e3a5f] px-8 py-3 rounded-full font-semibold text-lg" 
                  whileHover={{
                    scale: 1.05
                  }} 
                  whileTap={{
                    scale: 0.95
                  }} 
                  transition={{
                    type: 'spring',
                    stiffness: 400,
                    damping: 17
                  }}
                >
                  {slides[currentSlide].secondaryButton}
                </motion.button>
              </motion.div>
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      {/* Navigation Controls */}
      <div className="absolute inset-0 z-30 flex items-center justify-between px-4 md:px-8">
        {/* Previous Button */}
        <motion.button
          onClick={prevSlide}
          className="bg-white/20 hover:bg-white/30 text-white p-3 rounded-full backdrop-blur-sm transition-colors"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <ChevronLeftIcon className="h-6 w-6" />
        </motion.button>

        {/* Next Button */}
        <motion.button
          onClick={nextSlide}
          className="bg-white/20 hover:bg-white/30 text-white p-3 rounded-full backdrop-blur-sm transition-colors"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <ChevronRightIcon className="h-6 w-6" />
        </motion.button>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30 flex space-x-3">
        {slides.map((_, index) => (
          <motion.button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide 
                ? 'bg-white scale-125' 
                : 'bg-white/50 hover:bg-white/75'
            }`}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
          />
        ))}
      </div>

      {/* Progress Bar */}
      <div className="absolute bottom-0 left-0 z-30 w-full h-1 bg-white/20">
        <motion.div
          className="h-full bg-white"
          initial={{ width: 0 }}
          animate={{ width: `${((currentSlide + 1) / slides.length) * 100}%` }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
        />
      </div>
    </section>
  );
};

export default HeroSection;