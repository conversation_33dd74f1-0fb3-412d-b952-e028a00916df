import React, { useEffect, useRef, useState } from 'react';
import { motion, useAnimation, useInView, AnimatePresence, useScroll, useTransform, useMotionValue, useSpring } from 'framer-motion';
import { ArrowRightIcon, ChevronLeftIcon, ChevronRightIcon, PlayIcon, PauseIcon } from 'lucide-react';

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);
  const heroRef = useRef<HTMLElement>(null);

  // Parallax scroll effects
  const { scrollY } = useScroll();
  const backgroundY = useTransform(scrollY, [0, 1000], [0, -300]);
  const contentY = useTransform(scrollY, [0, 1000], [0, -150]);
  const overlayOpacity = useTransform(scrollY, [0, 500], [0.4, 0.8]);

  // Mouse parallax effect
  const mouseX = useMotionValue(0);
  const mouseY = useMotionValue(0);
  const mouseXSpring = useSpring(mouseX, { stiffness: 500, damping: 100 });
  const mouseYSpring = useSpring(mouseY, { stiffness: 500, damping: 100 });

  const slides = [
    {
      id: 1,
      backgroundImage: "url('https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80')",
      badge: "TRUSTED AUTOMOTIVE EXPERTS",
      title: "Dealership Quality Repair at an Affordable Price",
      description: "Specializing in transmission rebuilding, engine repair, and advanced diagnostics with over 38 years of combined experience.",
      primaryButton: "Book Appointment",
      secondaryButton: "View Cars for Sale",
      particles: [
        { x: 20, y: 30, size: 4, speed: 0.5 },
        { x: 80, y: 20, size: 6, speed: 0.3 },
        { x: 60, y: 70, size: 3, speed: 0.7 },
        { x: 30, y: 80, size: 5, speed: 0.4 },
        { x: 90, y: 50, size: 4, speed: 0.6 }
      ]
    },
    {
      id: 2,
      backgroundImage: "url('https://images.unsplash.com/photo-1486262715619-67b85e0b08d3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80')",
      badge: "EXPERT TRANSMISSION SERVICE",
      title: "Professional Transmission Rebuilding & Repair",
      description: "Complete transmission overhauls with 1-year labor warranty. From diagnostics to full rebuilds, we handle all transmission needs.",
      primaryButton: "Transmission Services",
      secondaryButton: "Get Free Quote",
      particles: [
        { x: 15, y: 25, size: 5, speed: 0.4 },
        { x: 85, y: 15, size: 3, speed: 0.8 },
        { x: 50, y: 75, size: 6, speed: 0.3 },
        { x: 25, y: 85, size: 4, speed: 0.5 },
        { x: 95, y: 45, size: 3, speed: 0.7 }
      ]
    },
    {
      id: 3,
      backgroundImage: "url('https://images.unsplash.com/photo-1601362840469-51e4d8d58785?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1335&q=80')",
      badge: "ADVANCED DIAGNOSTICS",
      title: "State-of-the-Art Diagnostic Equipment",
      description: "Using the latest Autel MaxiSys Ultra scan tool to diagnose and resolve even the most complex vehicle issues quickly and accurately.",
      primaryButton: "Diagnostic Services",
      secondaryButton: "Call (*************",
      particles: [
        { x: 10, y: 40, size: 4, speed: 0.6 },
        { x: 75, y: 10, size: 5, speed: 0.4 },
        { x: 45, y: 80, size: 3, speed: 0.8 },
        { x: 35, y: 60, size: 6, speed: 0.3 },
        { x: 85, y: 35, size: 4, speed: 0.5 }
      ]
    }
  ];

  // Handle mouse movement for parallax effect
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!heroRef.current) return;

    const rect = heroRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left - rect.width / 2) / rect.width;
    const y = (e.clientY - rect.top - rect.height / 2) / rect.height;

    setMousePosition({ x, y });
    mouseX.set(x * 20);
    mouseY.set(y * 20);
  };

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const toggleAutoPlay = () => {
    setIsAutoPlaying(!isAutoPlaying);
  };

  // Auto-play functionality
  useEffect(() => {
    if (isAutoPlaying) {
      autoPlayRef.current = setInterval(() => {
        nextSlide();
      }, 6000); // Change slide every 6 seconds
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [isAutoPlaying, currentSlide]);

  // Pause auto-play on hover
  const handleMouseEnter = () => setIsAutoPlaying(false);
  const handleMouseLeave = () => setIsAutoPlaying(true);

  const slideVariants = {
    enter: {
      opacity: 0,
      scale: 1.05,
      rotateY: 5
    },
    center: {
      opacity: 1,
      scale: 1,
      rotateY: 0,
      transition: {
        duration: 1.2,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    exit: {
      opacity: 0,
      scale: 0.95,
      rotateY: -5,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  const contentVariants = {
    hidden: {
      opacity: 0,
      y: 60,
      scale: 0.9
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        delay: 0.4,
        ease: [0.25, 0.46, 0.45, 0.94],
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: {
      opacity: 0,
      y: 30,
      scale: 0.9
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    }
  };

  return (
    <section
      ref={heroRef}
      className="relative w-full min-h-screen overflow-hidden"
      style={{ height: '100vh' }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseMove={handleMouseMove}
    >
      {/* Parallax Background Layers */}
      <AnimatePresence mode="wait">
        <motion.div
          key={currentSlide}
          className="absolute inset-0 z-0"
          variants={slideVariants}
          initial="enter"
          animate="center"
          exit="exit"
          style={{
            backgroundImage: slides[currentSlide].backgroundImage,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            width: '110%',
            height: '110%',
            left: '-5%',
            top: '-5%',
            y: backgroundY
          }}
        >
          {/* Dynamic Overlay with parallax opacity */}
          <motion.div
            className="absolute inset-0 z-10"
            style={{
              background: 'linear-gradient(135deg, rgba(30,58,95,0.3), rgba(15,37,66,0.6), rgba(0,0,0,0.4))',
              opacity: overlayOpacity
            }}
          />

          {/* Animated Particles */}
          {slides[currentSlide].particles.map((particle, index) => (
            <motion.div
              key={index}
              className="absolute rounded-full bg-white/20 backdrop-blur-sm"
              style={{
                left: `${particle.x}%`,
                top: `${particle.y}%`,
                width: `${particle.size}px`,
                height: `${particle.size}px`,
                x: mouseXSpring,
                y: mouseYSpring
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0.3, 0.8, 0.3],
                scale: [1, 1.2, 1]
              }}
              transition={{
                duration: 3 + particle.speed,
                repeat: Infinity,
                ease: "easeInOut",
                delay: index * 0.5
              }}
            />
          ))}
        </motion.div>
      </AnimatePresence>

      {/* Enhanced Content with Parallax */}
      <motion.div
        className="absolute inset-0 z-20"
        style={{ y: contentY }}
      >
        <div className="container mx-auto h-full flex items-center px-4 md:px-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentSlide}
              className="max-w-4xl"
              variants={contentVariants}
              initial="hidden"
              animate="visible"
              exit="hidden"
            >
              <motion.div
                className="mb-4"
                variants={itemVariants}
              >
                <motion.span
                  className="inline-block bg-gradient-to-r from-blue-500 to-blue-700 text-white px-6 py-2 rounded-full text-sm font-bold mb-4 shadow-lg backdrop-blur-sm"
                  whileHover={{ scale: 1.05, y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                >
                  {slides[currentSlide].badge}
                </motion.span>
              </motion.div>

              <motion.h1
                className="text-4xl md:text-6xl lg:text-7xl font-black text-white mb-6 leading-tight"
                variants={itemVariants}
                style={{
                  textShadow: '0 4px 20px rgba(0,0,0,0.5), 0 0 40px rgba(59,130,246,0.3)',
                  background: 'linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%)',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  backgroundClip: 'text'
                }}
              >
                {slides[currentSlide].title}
              </motion.h1>

              <motion.p
                className="text-xl md:text-2xl text-blue-100 mb-10 leading-relaxed max-w-2xl"
                variants={itemVariants}
                style={{
                  textShadow: '0 2px 10px rgba(0,0,0,0.3)'
                }}
              >
                {slides[currentSlide].description}
              </motion.p>

              <motion.div
                className="flex flex-wrap gap-6"
                variants={itemVariants}
              >
                <motion.button
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-10 py-4 rounded-full font-bold text-lg flex items-center shadow-2xl backdrop-blur-sm border border-blue-500/30"
                  whileHover={{
                    scale: 1.05,
                    y: -3,
                    boxShadow: "0 20px 40px rgba(59, 130, 246, 0.4)"
                  }}
                  whileTap={{
                    scale: 0.95
                  }}
                  transition={{
                    type: 'spring',
                    stiffness: 400,
                    damping: 17
                  }}
                >
                  {slides[currentSlide].primaryButton}
                  <ArrowRightIcon className="ml-3 h-6 w-6" />
                </motion.button>

                <motion.button
                  className="bg-white/10 hover:bg-white/20 backdrop-blur-md text-white border-2 border-white/30 hover:border-white/50 px-10 py-4 rounded-full font-bold text-lg shadow-2xl"
                  whileHover={{
                    scale: 1.05,
                    y: -3,
                    backgroundColor: "rgba(255,255,255,0.15)"
                  }}
                  whileTap={{
                    scale: 0.95
                  }}
                  transition={{
                    type: 'spring',
                    stiffness: 400,
                    damping: 17
                  }}
                >
                  {slides[currentSlide].secondaryButton}
                </motion.button>
              </motion.div>
            </motion.div>
          </AnimatePresence>
        </div>
      </motion.div>

      {/* Navigation Controls */}
      <div className="absolute inset-0 z-30 flex items-center justify-between px-4 md:px-8">
        {/* Previous Button */}
        <motion.button
          onClick={prevSlide}
          className="bg-white/20 hover:bg-white/30 text-white p-3 rounded-full backdrop-blur-sm transition-colors"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <ChevronLeftIcon className="h-6 w-6" />
        </motion.button>

        {/* Next Button */}
        <motion.button
          onClick={nextSlide}
          className="bg-white/20 hover:bg-white/30 text-white p-3 rounded-full backdrop-blur-sm transition-colors"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
        >
          <ChevronRightIcon className="h-6 w-6" />
        </motion.button>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-30 flex space-x-3">
        {slides.map((_, index) => (
          <motion.button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide 
                ? 'bg-white scale-125' 
                : 'bg-white/50 hover:bg-white/75'
            }`}
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
          />
        ))}
      </div>
    </section>
  );
};

export default HeroSection;