import React, { Children } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { PhoneIcon, MailIcon, MapPinIcon, InstagramIcon } from 'lucide-react';
const Footer = () => {
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.1
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 20,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  return <footer className="bg-[#1e3a5f] text-white pt-16 pb-8">
      <div className="container mx-auto px-4 md:px-8">
        <motion.div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12" variants={containerVariants} initial="hidden" whileInView="visible" viewport={{
        once: true,
        margin: '-100px'
      }}>
          <motion.div variants={itemVariants}>
            <h3 className="text-xl font-bold mb-6">ADJ Automotive</h3>
            <p className="mb-4 text-blue-100">
              Your trusted car repair experts providing dealership quality
              repairs at affordable prices.
            </p>
            <div className="flex items-center mb-3">
              <div className="bg-blue-600 p-2 rounded-full mr-3">
                <PhoneIcon className="h-4 w-4" />
              </div>
              <a href="tel:+16714838335" className="text-blue-100 hover:text-white transition-colors">
                (*************
              </a>
            </div>
            <div className="flex items-center mb-3">
              <div className="bg-blue-600 p-2 rounded-full mr-3">
                <MailIcon className="h-4 w-4" />
              </div>
              <a href="mailto:<EMAIL>" className="text-blue-100 hover:text-white transition-colors">
                <EMAIL>
              </a>
            </div>
            <div className="flex items-start mb-3">
              <div className="bg-blue-600 p-2 rounded-full mr-3 mt-1">
                <MapPinIcon className="h-4 w-4" />
              </div>
              <address className="text-blue-100 not-italic">
                125 Chalan Ayuyu Yigo,
                <br />
                Guam 96929
              </address>
            </div>
          </motion.div>
          <motion.div variants={itemVariants}>
            <h3 className="text-xl font-bold mb-6">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/" className="text-blue-100 hover:text-white transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link to="/services" className="text-blue-100 hover:text-white transition-colors">
                  Services
                </Link>
              </li>
              <li>
                <Link to="/cars" className="text-blue-100 hover:text-white transition-colors">
                  Cars for Sale
                </Link>
              </li>
              <li>
                <Link to="/about" className="text-blue-100 hover:text-white transition-colors">
                  About Us
                </Link>
              </li>
              <li>
                <Link to="/contact" className="text-blue-100 hover:text-white transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </motion.div>
          <motion.div variants={itemVariants}>
            <h3 className="text-xl font-bold mb-6">Our Services</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/services#transmission" className="text-blue-100 hover:text-white transition-colors">
                  Transmission Rebuilding
                </Link>
              </li>
              <li>
                <Link to="/services#engine" className="text-blue-100 hover:text-white transition-colors">
                  Engine Repair
                </Link>
              </li>
              <li>
                <Link to="/services#diagnostics" className="text-blue-100 hover:text-white transition-colors">
                  Advanced Diagnostics
                </Link>
              </li>
              <li>
                <Link to="/services#brakes" className="text-blue-100 hover:text-white transition-colors">
                  Brake Service
                </Link>
              </li>
              <li>
                <Link to="/services#electrical" className="text-blue-100 hover:text-white transition-colors">
                  Electrical Service
                </Link>
              </li>
            </ul>
          </motion.div>
          <motion.div variants={itemVariants}>
            <h3 className="text-xl font-bold mb-6">Business Hours</h3>
            <ul className="space-y-3">
              <li className="flex justify-between">
                <span className="text-blue-100">Monday - Friday:</span>
                <span className="text-white">8:00 AM - 5:00 PM</span>
              </li>
              <li className="flex justify-between">
                <span className="text-blue-100">Saturday:</span>
                <span className="text-white">9:00 AM - 2:00 PM</span>
              </li>
              <li className="flex justify-between">
                <span className="text-blue-100">Sunday:</span>
                <span className="text-white">Closed</span>
              </li>
            </ul>
            <div className="mt-6">
              <h4 className="font-semibold mb-2">Follow Us</h4>
              <div className="flex space-x-3">
                <a href="https://instagram.com/adjauto" target="_blank" rel="noopener noreferrer" className="bg-blue-600 hover:bg-blue-700 p-2 rounded-full transition-colors">
                  <InstagramIcon className="h-5 w-5" />
                </a>
              </div>
            </div>
          </motion.div>
        </motion.div>
        <motion.div className="border-t border-blue-800 pt-8 text-center" variants={itemVariants} initial="hidden" whileInView="visible" viewport={{
        once: true
      }}>
          <p className="text-blue-200 text-sm">
            &copy; {new Date().getFullYear()} ADJ Automotive Repair Services.
            All rights reserved.
            <br />
            Veteran Owned Small Business (VOSB)
          </p>
        </motion.div>
      </div>
    </footer>;
};
export default Footer;