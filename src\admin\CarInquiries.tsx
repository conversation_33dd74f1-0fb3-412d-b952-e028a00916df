import React, { useState } from 'react';
import { SearchIcon, EyeIcon, CheckIcon, XIcon, ClockIcon, ArrowUpDownIcon, FilterIcon, ChevronLeftIcon, ChevronRightIcon, PhoneIcon, MailIcon, MessageSquareIcon, CalendarIcon, FileTextIcon, MoreHorizontalIcon, ArrowLeftIcon, MenuIcon, CarIcon } from 'lucide-react';
import AdminSidebar from '../components/AdminSidebar';

// Move getStatusColor function outside components so both can access it
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'contacted':
      return 'bg-blue-100 text-blue-800';
    case 'scheduled':
      return 'bg-purple-100 text-purple-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'not interested':
      return 'bg-red-100 text-red-800';
    case 'sold':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const AdminCarInquiries = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState('inquiryDate');
  const [sortDirection, setSortDirection] = useState('desc');
  const [selectedInquiry, setSelectedInquiry] = useState(null);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Sample car inquiries data
  const carInquiries = [
    {
      id: 'CI-2023-089',
      customer: {
        name: 'Michael Rodriguez',
        email: '<EMAIL>',
        phone: '(*************'
      },
      vehicle: {
        year: 2019,
        make: 'Ford',
        model: 'Mustang GT Premium',
        stockNumber: 'P12345',
        price: '$32,500',
        vin: '1FA6P8CF3K5187429'
      },
      status: 'Contacted',
      inquiryDate: '2023-10-12',
      preferredContact: 'Phone',
      message: "I'm interested in the 2019 Ford Mustang GT Premium. Can we schedule a test drive?",
      notes: 'Customer called back. Very interested. Scheduled test drive for Oct 15.',
      assignedTo: 'David Chen'
    },
    {
      id: 'CI-2023-088',
      customer: {
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '(*************'
      },
      vehicle: {
        year: 2020,
        make: 'Chevrolet',
        model: 'Camaro SS',
        stockNumber: 'P12346',
        price: '$35,900',
        vin: '1G1FB1RS8L0123456'
      },
      status: 'Sold',
      inquiryDate: '2023-10-11',
      preferredContact: 'Email',
      message: "Interested in financing options for the Camaro SS. What's available?",
      notes: 'Customer purchased the vehicle. Financing approved through our partner.',
      assignedTo: 'Adam Johnson',
      saleDate: '2023-10-14',
      salePrice: '$35,900'
    },
    {
      id: 'CI-2023-087',
      customer: {
        name: 'David Chen',
        email: '<EMAIL>',
        phone: '(*************'
      },
      vehicle: {
        year: 2018,
        make: 'Dodge',
        model: 'Challenger R/T',
        stockNumber: 'P12347',
        price: '$29,500',
        vin: '2C3CDZAG7JH123456'
      },
      status: 'Scheduled',
      inquiryDate: '2023-10-10',
      preferredContact: 'Text',
      message: "Can I get more details about the maintenance history of this Challenger?",
      scheduledDate: '2023-10-15',
      assignedTo: 'Sarah Williams'
    },
    {
      id: 'CI-2023-086',
      customer: {
        name: 'Emily Wilson',
        email: '<EMAIL>',
        phone: '(*************'
      },
      vehicle: {
        year: 2021,
        make: 'Ford',
        model: 'Mustang EcoBoost',
        stockNumber: 'P12348',
        price: '$28,750',
        vin: '1FA6P8THXL5123456'
      },
      status: 'Pending',
      inquiryDate: '2023-10-09',
      preferredContact: 'Email',
      message: "Looking for a reliable sports car. Is this EcoBoost a good choice for daily driving?"
    },
    {
      id: 'CI-2023-085',
      customer: {
        name: 'Robert Garcia',
        email: '<EMAIL>',
        phone: '(*************'
      },
      vehicle: {
        year: 2019,
        make: 'BMW',
        model: '330i',
        stockNumber: 'P12349',
        price: '$26,900',
        vin: 'WBA5R1C51K1234567'
      },
      status: 'Not Interested',
      inquiryDate: '2023-10-08',
      preferredContact: 'Phone',
      message: "Interested in the BMW 330i. Can you tell me about warranty options?",
      notes: 'Customer decided to go with a newer model at another dealer.',
      assignedTo: 'Michael Chen'
    },
    {
      id: 'CI-2023-084',
      customer: {
        name: 'Jennifer Lee',
        email: '<EMAIL>',
        phone: '(*************'
      },
      vehicle: {
        year: 2017,
        make: 'Audi',
        model: 'A4 Premium',
        stockNumber: 'P12350',
        price: '$22,500',
        vin: 'WAUDNAF41HN123456'
      },
      status: 'Completed',
      inquiryDate: '2023-10-07',
      preferredContact: 'Email',
      message: "Can we negotiate on the price? I'm a serious buyer.",
      notes: 'Customer completed test drive but decided not to purchase.',
      assignedTo: 'David Chen'
    }
  ];

  // Filter inquiries based on search query and status
  const filteredInquiries = carInquiries.filter(inquiry => {
    const matchesSearch = 
      inquiry.customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      inquiry.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      `${inquiry.vehicle.year} ${inquiry.vehicle.make} ${inquiry.vehicle.model}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
      inquiry.vehicle.stockNumber.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || inquiry.status.toLowerCase() === statusFilter.toLowerCase();
    return matchesSearch && matchesStatus;
  });

  // Sort inquiries
  const sortedInquiries = [...filteredInquiries].sort((a, b) => {
    if (sortField === 'inquiryDate') {
      return sortDirection === 'asc' 
        ? new Date(a.inquiryDate).getTime() - new Date(b.inquiryDate).getTime()
        : new Date(b.inquiryDate).getTime() - new Date(a.inquiryDate).getTime();
    }
    if (sortField === 'scheduledDate') {
      // Handle null scheduled dates
      if (!a.scheduledDate) return sortDirection === 'asc' ? 1 : -1;
      if (!b.scheduledDate) return sortDirection === 'asc' ? -1 : 1;
      return sortDirection === 'asc' 
        ? new Date(a.scheduledDate).getTime() - new Date(b.scheduledDate).getTime()
        : new Date(b.scheduledDate).getTime() - new Date(a.scheduledDate).getTime();
    }
    if (sortField === 'customer') {
      return sortDirection === 'asc' 
        ? a.customer.name.localeCompare(b.customer.name)
        : b.customer.name.localeCompare(a.customer.name);
    }
    return 0;
  });

  // Pagination
  const itemsPerPage = 10;
  const totalPages = Math.ceil(sortedInquiries.length / itemsPerPage);
  const paginatedInquiries = sortedInquiries.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleViewInquiry = (inquiry: any) => {
    setSelectedInquiry(inquiry);
  };

  if (selectedInquiry) {
    return (
      <CarInquiryDetail 
        inquiry={selectedInquiry} 
        onBack={() => setSelectedInquiry(null)} 
      />
    );
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
        <AdminSidebar activePage="car-inquiries" />
      </aside>

      {/* Mobile sidebar */}
      <div className={`fixed inset-0 bg-gray-800 bg-opacity-75 z-40 md:hidden transition-opacity duration-200 ${sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`} onClick={toggleSidebar}></div>

      <aside className={`fixed top-0 left-0 h-full bg-[#1e3a5f] text-white w-64 z-50 transform transition-transform duration-200 ease-in-out md:hidden ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="absolute top-4 right-4">
          <button className="text-white p-2" onClick={toggleSidebar}>
            <XIcon className="h-6 w-6" />
          </button>
        </div>
        <AdminSidebar activePage="car-inquiries" />
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top header */}
        <header className="bg-white shadow-sm z-10">
          <div className="px-4 py-4 flex justify-between items-center">
            <div className="flex items-center md:hidden">
              <button className="text-gray-600 p-2" onClick={toggleSidebar}>
                <MenuIcon className="h-6 w-6" />
              </button>
            </div>
            <h1 className="text-xl font-semibold text-gray-800">
              Car Inquiries
            </h1>
            <div></div>
          </div>
        </header>

        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-gray-100 p-4">
          <div className="bg-white rounded-xl shadow-md p-4 mb-6">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div className="flex-1">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search by customer, ID, vehicle, or stock number..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <span className="text-gray-600 mr-2">Status:</span>
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="all">All</option>
                    <option value="pending">Pending</option>
                    <option value="contacted">Contacted</option>
                    <option value="scheduled">Scheduled</option>
                    <option value="completed">Completed</option>
                    <option value="sold">Sold</option>
                    <option value="not interested">Not Interested</option>
                  </select>
                </div>
                <button className="p-2 text-gray-600 hover:text-gray-800">
                  <FilterIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead>
                  <tr className="bg-gray-100 border-b border-gray-200">
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      ID
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button
                        onClick={() => handleSort('customer')}
                        className="flex items-center"
                      >
                        Customer
                        <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vehicle
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <button
                        onClick={() => handleSort('inquiryDate')}
                        className="flex items-center"
                      >
                        Inquiry Date
                        <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Contact Method
                    </th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {paginatedInquiries.map((inquiry) => (
                    <tr key={inquiry.id} className="hover:bg-gray-50">
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                        {inquiry.id}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {inquiry.customer.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {inquiry.customer.phone}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {inquiry.vehicle.year} {inquiry.vehicle.make} {inquiry.vehicle.model}
                        </div>
                        <div className="text-sm text-gray-500">
                          Stock #: {inquiry.vehicle.stockNumber}
                        </div>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {inquiry.vehicle.price}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap">
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(inquiry.status)}`}>
                          {inquiry.status}
                        </span>
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {inquiry.inquiryDate}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                        {inquiry.preferredContact}
                      </td>
                      <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleViewInquiry(inquiry)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <EyeIcon className="h-5 w-5" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200">
                <div className="flex-1 flex justify-between items-center">
                  <p className="text-sm text-gray-700">
                    Showing{' '}
                    <span className="font-medium">
                      {(currentPage - 1) * itemsPerPage + 1}
                    </span>{' '}
                    to{' '}
                    <span className="font-medium">
                      {Math.min(currentPage * itemsPerPage, sortedInquiries.length)}
                    </span>{' '}
                    of{' '}
                    <span className="font-medium">{sortedInquiries.length}</span>{' '}
                    results
                  </p>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage === 1}
                      className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white ${
                        currentPage === 1 
                          ? 'text-gray-300' 
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <ChevronLeftIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white ${
                        currentPage === totalPages 
                          ? 'text-gray-300' 
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <ChevronRightIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

const CarInquiryDetail = ({ inquiry, onBack }: { inquiry: any; onBack: () => void }) => {
  const [status, setStatus] = useState(inquiry.status);
  const [notes, setNotes] = useState(inquiry.notes || '');
  const [assignedTo, setAssignedTo] = useState(inquiry.assignedTo || '');
  const [scheduledDate, setScheduledDate] = useState(inquiry.scheduledDate || '');
  const [salePrice, setSalePrice] = useState(inquiry.salePrice || '');

  const statusOptions = ['Pending', 'Contacted', 'Scheduled', 'Completed', 'Sold', 'Not Interested'];
  const salesTeam = ['Adam Johnson', 'Sarah Williams', 'David Chen', 'Michael Chen', 'Jennifer Lee'];

  const handleUpdateInquiry = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would send data to your backend
    console.log('Inquiry updated:', {
      id: inquiry.id,
      status,
      notes,
      assignedTo,
      scheduledDate,
      salePrice
    });
    onBack();
  };

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
        <AdminSidebar activePage="car-inquiries" />
      </aside>

      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto bg-gray-100 p-6">
          <button
            onClick={onBack}
            className="flex items-center text-blue-600 hover:text-blue-800 mb-6"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Car Inquiries
          </button>

          <div className="bg-white rounded-xl shadow-md overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <div>
                <h2 className="text-xl font-bold text-gray-900">
                  Car Inquiry: {inquiry.id}
                </h2>
                <p className="text-gray-600">
                  {inquiry.vehicle.year} {inquiry.vehicle.make} {inquiry.vehicle.model} - {inquiry.vehicle.price}
                </p>
              </div>
              <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full ${getStatusColor(inquiry.status)}`}>
                {inquiry.status}
              </span>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Customer Information */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Customer Information
                  </h3>
                  <div className="space-y-3">
                    <div>
                      <p className="text-gray-500 text-sm">Name</p>
                      <p className="font-medium">{inquiry.customer.name}</p>
                    </div>
                    <div className="flex items-center">
                      <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a 
                        href={`tel:${inquiry.customer.phone}`} 
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {inquiry.customer.phone}
                      </a>
                    </div>
                    <div className="flex items-center">
                      <MailIcon className="h-4 w-4 text-gray-400 mr-2" />
                      <a 
                        href={`mailto:${inquiry.customer.email}`} 
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {inquiry.customer.email}
                      </a>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Preferred Contact</p>
                      <p className="font-medium">{inquiry.preferredContact}</p>
                    </div>
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-200 flex space-x-2">
                    <button className="p-2 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-50">
                      <PhoneIcon className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-50">
                      <MailIcon className="h-5 w-5" />
                    </button>
                    <button className="p-2 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-50">
                      <MessageSquareIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>

                {/* Vehicle & Inquiry Details */}
                <div className="md:col-span-2 bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Vehicle & Inquiry Details
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-gray-500 text-sm">Vehicle</p>
                      <div className="flex items-center">
                        <CarIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <p className="font-medium">{inquiry.vehicle.year} {inquiry.vehicle.make} {inquiry.vehicle.model}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Stock Number</p>
                      <p className="font-medium">{inquiry.vehicle.stockNumber}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">VIN</p>
                      <p className="font-medium">{inquiry.vehicle.vin}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Listed Price</p>
                      <p className="font-medium text-blue-700">{inquiry.vehicle.price}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Inquiry Date</p>
                      <div className="flex items-center">
                        <CalendarIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <p>{inquiry.inquiryDate}</p>
                      </div>
                    </div>
                    {inquiry.scheduledDate && (
                      <div>
                        <p className="text-gray-500 text-sm">Scheduled Date</p>
                        <div className="flex items-center">
                          <ClockIcon className="h-4 w-4 text-gray-400 mr-2" />
                          <p>{inquiry.scheduledDate}</p>
                        </div>
                      </div>
                    )}
                    {inquiry.assignedTo && (
                      <div>
                        <p className="text-gray-500 text-sm">Assigned To</p>
                        <p className="font-medium">{inquiry.assignedTo}</p>
                      </div>
                    )}
                    {inquiry.saleDate && (
                      <div>
                        <p className="text-gray-500 text-sm">Sale Date</p>
                        <div className="flex items-center">
                          <CheckIcon className="h-4 w-4 text-green-500 mr-2" />
                          <p>{inquiry.saleDate}</p>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-200">
                    <p className="text-gray-500 text-sm mb-2">Customer Message</p>
                    <p className="bg-white p-3 rounded border border-gray-200">
                      {inquiry.message}
                    </p>
                  </div>
                  {inquiry.notes && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <p className="text-gray-500 text-sm mb-2">Notes</p>
                      <p className="bg-white p-3 rounded border border-gray-200">
                        {inquiry.notes}
                      </p>
                    </div>
                  )}
                  {inquiry.salePrice && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <p className="text-gray-500 text-sm">Final Sale Price</p>
                      <p className="font-semibold text-lg text-green-700">
                        {inquiry.salePrice}
                      </p>
                    </div>
                  )}
                </div>

                {/* Update Form */}
                <div className="md:col-span-3">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Update Car Inquiry
                  </h3>
                  <form onSubmit={handleUpdateInquiry}>
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                      <div>
                        <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                          Status
                        </label>
                        <select
                          id="status"
                          value={status}
                          onChange={(e) => setStatus(e.target.value)}
                          className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          {statusOptions.map((option) => (
                            <option key={option} value={option}>
                              {option}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label htmlFor="assignedTo" className="block text-sm font-medium text-gray-700 mb-1">
                          Assigned To
                        </label>
                        <select
                          id="assignedTo"
                          value={assignedTo}
                          onChange={(e) => setAssignedTo(e.target.value)}
                          className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="">Select Sales Rep</option>
                          {salesTeam.map((rep) => (
                            <option key={rep} value={rep}>
                              {rep}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700 mb-1">
                          Scheduled Date
                        </label>
                        <input
                          type="date"
                          id="scheduledDate"
                          value={scheduledDate}
                          onChange={(e) => setScheduledDate(e.target.value)}
                          className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                      <div>
                        <label htmlFor="salePrice" className="block text-sm font-medium text-gray-700 mb-1">
                          Sale Price
                        </label>
                        <input
                          type="text"
                          id="salePrice"
                          value={salePrice}
                          onChange={(e) => setSalePrice(e.target.value)}
                          placeholder="$0.00"
                          className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                      </div>
                    </div>
                    <div className="mb-6">
                      <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                        Notes
                      </label>
                      <textarea
                        id="notes"
                        value={notes}
                        onChange={(e) => setNotes(e.target.value)}
                        rows={4}
                        className="w-full px-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Add notes about the customer inquiry..."
                      ></textarea>
                    </div>
                    <div className="flex justify-between">
                      <div className="flex space-x-2">
                        <button
                          type="button"
                          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center"
                        >
                          <FileTextIcon className="h-5 w-5 mr-2" />
                          Generate Quote
                        </button>
                        <button
                          type="button"
                          className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 flex items-center"
                        >
                          <MessageSquareIcon className="h-5 w-5 mr-2" />
                          Message Customer
                        </button>
                        <div className="relative">
                          <button
                            type="button"
                            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
                          >
                            <MoreHorizontalIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                      <div>
                        <button
                          type="submit"
                          className="px-6 py-2 bg-[#1e3a5f] hover:bg-blue-800 text-white rounded-lg transition-colors"
                        >
                          Update Inquiry
                        </button>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminCarInquiries;