import { useState } from 'react';
import { motion } from 'framer-motion';
import { CarIcon, ClipboardListIcon, UsersIcon, SettingsIcon, TrendingUpIcon, CalendarIcon, DollarSignIcon, AlertCircleIcon, CheckCircleIcon, FileTextIcon, BellIcon, MenuIcon, XIcon, MessageSquareIcon } from 'lucide-react';
import { Link } from 'react-router-dom';
import AdminSidebar from '../components/AdminSidebar';
const Dashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };
  return <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <aside className={`bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block`}>
        <AdminSidebar activePage="dashboard" />
      </aside>
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 bg-gray-800 bg-opacity-75 z-40 md:hidden transition-opacity duration-200 ${sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`} onClick={toggleSidebar}></div>
      <aside className={`fixed top-0 left-0 h-full bg-[#1e3a5f] text-white w-64 z-50 transform transition-transform duration-200 ease-in-out md:hidden ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="absolute top-4 right-4">
          <button className="text-white p-2" onClick={toggleSidebar}>
            <XIcon className="h-6 w-6" />
          </button>
        </div>
        <AdminSidebar activePage="dashboard" />
      </aside>
      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top header */}
        <header className="bg-white shadow-sm z-10">
          <div className="px-4 py-4 flex justify-between items-center">
            <div className="flex items-center md:hidden">
              <button className="text-gray-600 p-2" onClick={toggleSidebar}>
                <MenuIcon className="h-6 w-6" />
              </button>
            </div>
            <h1 className="text-xl font-semibold text-gray-800">
              Admin Dashboard
            </h1>
            <div className="flex items-center space-x-4">
              <button className="p-1 text-gray-400 hover:text-gray-600 relative">
                <BellIcon className="h-6 w-6" />
                <span className="absolute top-0 right-0 h-2 w-2 bg-red-500 rounded-full"></span>
              </button>
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center text-white font-medium">
                  A
                </div>
                <span className="text-sm font-medium text-gray-700 hidden sm:block">
                  Admin
                </span>
              </div>
            </div>
          </div>
        </header>
        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-gray-100 p-4">
          <DashboardContent />
        </main>
      </div>
    </div>;
};


const DashboardContent = () => {
  const stats = [{
    title: 'Total Service Requests',
    value: '124',
    icon: <ClipboardListIcon className="h-6 w-6" />,
    change: '+12%',
    color: 'bg-blue-500'
  }, {
    title: 'Car Inquiries',
    value: '42',
    icon: <MessageSquareIcon className="h-6 w-6" />,
    change: '+15%',
    color: 'bg-indigo-500'
  }, {
    title: 'Cars in Inventory',
    value: '36',
    icon: <CarIcon className="h-6 w-6" />,
    change: '+3%',
    color: 'bg-green-500'
  }, {
    title: 'Monthly Revenue',
    value: '$48,294',
    icon: <DollarSignIcon className="h-6 w-6" />,
    change: '+8%',
    color: 'bg-yellow-500'
  }];
  const recentRequests = [{
    id: 'SR-2023-089',
    customer: 'Michael Rodriguez',
    service: 'Transmission Rebuilding',
    vehicle: '2018 Toyota Camry',
    status: 'In Progress',
    date: 'Oct 12, 2023'
  }, {
    id: 'SR-2023-088',
    customer: 'Sarah Johnson',
    service: 'Engine Diagnostics',
    vehicle: '2020 Ford F-150',
    status: 'Completed',
    date: 'Oct 11, 2023'
  }, {
    id: 'SR-2023-087',
    customer: 'David Chen',
    service: 'Brake Service',
    vehicle: '2019 Mercedes C300',
    status: 'Scheduled',
    date: 'Oct 15, 2023'
  }, {
    id: 'SR-2023-086',
    customer: 'Emily Wilson',
    service: 'Key Programming',
    vehicle: '2017 Lexus RX350',
    status: 'Pending',
    date: 'Oct 14, 2023'
  }];

  const recentCarInquiries = [{
    id: 'CI-2023-089',
    customer: 'Michael Rodriguez',
    vehicle: '2019 Ford Mustang GT Premium',
    price: '$32,500',
    status: 'Contacted',
    date: 'Oct 12, 2023'
  }, {
    id: 'CI-2023-088',
    customer: 'Sarah Johnson',
    vehicle: '2020 Chevrolet Camaro SS',
    price: '$35,900',
    status: 'Sold',
    date: 'Oct 11, 2023'
  }, {
    id: 'CI-2023-087',
    customer: 'David Chen',
    vehicle: '2018 Dodge Challenger R/T',
    price: '$29,500',
    status: 'Scheduled',
    date: 'Oct 10, 2023'
  }, {
    id: 'CI-2023-086',
    customer: 'Emily Wilson',
    vehicle: '2021 Ford Mustang EcoBoost',
    price: '$28,750',
    status: 'Pending',
    date: 'Oct 09, 2023'
  }];
  const upcomingAppointments = [{
    id: 'AP-2023-045',
    customer: 'David Chen',
    service: 'Brake Service',
    time: '9:00 AM',
    vehicle: '2019 Mercedes C300'
  }, {
    id: 'AP-2023-046',
    customer: 'Emily Wilson',
    service: 'Key Programming',
    time: '11:30 AM',
    vehicle: '2017 Lexus RX350'
  }, {
    id: 'AP-2023-047',
    customer: 'Robert Garcia',
    service: 'Oil Change',
    time: '2:00 PM',
    vehicle: '2021 Chevrolet Tahoe'
  }];
  return <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => <motion.div key={index} className="bg-white rounded-xl shadow-sm p-6" initial={{
        opacity: 0,
        y: 20
      }} animate={{
        opacity: 1,
        y: 0
      }} transition={{
        delay: index * 0.1
      }}>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold text-gray-900 mt-1">
                  {stat.value}
                </p>
              </div>
              <div className={`${stat.color} text-white p-3 rounded-lg`}>
                {stat.icon}
              </div>
            </div>
            <div className="mt-4 flex items-center">
              <span className="text-green-600 text-sm font-medium flex items-center">
                <TrendingUpIcon className="h-4 w-4 mr-1" />
                {stat.change}
              </span>
              <span className="text-gray-500 text-sm ml-2">
                from last month
              </span>
            </div>
          </motion.div>)}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div className="lg:col-span-2 bg-white rounded-xl shadow-sm p-6" initial={{
        opacity: 0,
        y: 20
      }} animate={{
        opacity: 1,
        y: 0
      }} transition={{
        delay: 0.4
      }}>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900">
              Recent Service Requests
            </h2>
            <Link to="/admin/service-requests" className="text-sm font-medium text-blue-600 hover:text-blue-800">
              View All
            </Link>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Service
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Vehicle
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody>
                {recentRequests.map((request, index) => <tr key={index} className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-blue-600">
                      {request.id}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                      {request.customer}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                      {request.service}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                      {request.vehicle}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${request.status === 'Completed' ? 'bg-green-100 text-green-800' : request.status === 'In Progress' ? 'bg-blue-100 text-blue-800' : request.status === 'Scheduled' ? 'bg-purple-100 text-purple-800' : 'bg-yellow-100 text-yellow-800'}`}>
                        {request.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                      {request.date}
                    </td>
                  </tr>)}
              </tbody>
            </table>
          </div>
        </motion.div>
        <motion.div className="bg-white rounded-xl shadow-sm p-6" initial={{
        opacity: 0,
        y: 20
      }} animate={{
        opacity: 1,
        y: 0
      }} transition={{
        delay: 0.5
      }}>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900">
              Today's Appointments
            </h2>
            <div className="text-sm font-medium text-gray-500">
              <CalendarIcon className="h-4 w-4 inline mr-1" />
              {new Date().toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric'
            })}
            </div>
          </div>
          <div className="space-y-4">
            {upcomingAppointments.map((appointment, index) => <div key={index} className="p-4 border border-gray-200 rounded-lg hover:bg-blue-50 transition-colors">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium text-gray-900">
                      {appointment.customer}
                    </p>
                    <p className="text-sm text-gray-500">
                      {appointment.vehicle}
                    </p>
                  </div>
                  <div className="bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded">
                    {appointment.time}
                  </div>
                </div>
                <p className="text-sm text-gray-600 mt-2">
                  {appointment.service}
                </p>
              </div>)}
          </div>
          <Link 
            to="/admin/service-requests?status=scheduled&view=schedule" 
            className="w-full mt-4 bg-gray-100 hover:bg-gray-200 text-gray-800 font-medium py-2 rounded-lg transition-colors text-center block"
          >
            View Full Schedule
          </Link>
        </motion.div>
      </div>

      {/* Recent Car Inquiries Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div 
          className="lg:col-span-2 bg-white rounded-xl shadow-sm p-6" 
          initial={{ opacity: 0, y: 20 }} 
          animate={{ opacity: 1, y: 0 }} 
          transition={{ delay: 0.55 }}
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              Recent Car Inquiries
            </h2>
            <Link 
              to="/admin/car-inquiries" 
              className="text-sm font-medium text-blue-600 hover:text-blue-800"
            >
              View All
            </Link>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Vehicle
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                </tr>
              </thead>
              <tbody>
                {recentCarInquiries.map((inquiry, index) => (
                  <tr key={index} className="border-b border-gray-200 hover:bg-gray-50">
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-blue-600">
                      {inquiry.id}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900">
                      {inquiry.customer}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                      {inquiry.vehicle}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900">
                      {inquiry.price}
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        inquiry.status === 'Sold' ? 'bg-green-100 text-green-800' : 
                        inquiry.status === 'Contacted' ? 'bg-blue-100 text-blue-800' : 
                        inquiry.status === 'Scheduled' ? 'bg-purple-100 text-purple-800' : 
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {inquiry.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                      {inquiry.date}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </motion.div>

        {/* Car Inquiry Summary */}
        <motion.div 
          className="bg-white rounded-xl shadow-sm p-6" 
          initial={{ opacity: 0, y: 20 }} 
          animate={{ opacity: 1, y: 0 }} 
          transition={{ delay: 0.6 }}
        >
          <h2 className="text-lg font-semibold text-gray-900 mb-6">
            Inquiry Summary
          </h2>
          <div className="space-y-4">
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">Total Inquiries</span>
                <span className="text-xl font-bold text-gray-900">42</span>
              </div>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">This Month</span>
                <span className="text-xl font-bold text-blue-600">18</span>
              </div>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">Conversion Rate</span>
                <span className="text-xl font-bold text-green-600">23%</span>
              </div>
            </div>
            <div className="p-4 border border-gray-200 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">Avg. Response Time</span>
                <span className="text-xl font-bold text-purple-600">4.2h</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div className="bg-white rounded-xl shadow-sm p-6" initial={{
        opacity: 0,
        y: 20
      }} animate={{
        opacity: 1,
        y: 0
      }} transition={{
        delay: 0.6
      }}>
          <h2 className="text-lg font-semibold text-gray-900 mb-6">
            Monthly Revenue
          </h2>
          <div className="h-64 flex items-end justify-between px-4">
            {/* Placeholder for chart - in a real app you would use a charting library */}
            {[65, 40, 85, 30, 55, 60, 75, 40, 80, 55, 45, 70].map((height, index) => <div key={index} className="flex flex-col items-center">
                  <div className="w-8 bg-blue-500 rounded-t-lg transition-all duration-1000" style={{
              height: `${height}%`
            }}></div>
                  <div className="text-xs text-gray-500 mt-2">
                    {new Date(0, index).toLocaleString('default', {
                month: 'short'
              })}
                  </div>
                </div>)}
          </div>
        </motion.div>
        <motion.div className="bg-white rounded-xl shadow-sm p-6" initial={{
        opacity: 0,
        y: 20
      }} animate={{
        opacity: 1,
        y: 0
      }} transition={{
        delay: 0.7
      }}>
          <h2 className="text-lg font-semibold text-gray-900 mb-6">
            Service Distribution
          </h2>
          <div className="flex items-center justify-center h-64">
            {/* Placeholder for pie chart - in a real app you would use a charting library */}
            <div className="relative h-48 w-48">
              <div className="absolute inset-0 rounded-full border-8 border-t-blue-500 border-r-purple-500 border-b-green-500 border-l-yellow-500 transform rotate-45"></div>
              <div className="absolute inset-4 bg-white rounded-full flex items-center justify-center">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-800">124</div>
                  <div className="text-xs text-gray-500">Total Services</div>
                </div>
              </div>
            </div>
            <div className="ml-8 space-y-3">
              <div className="flex items-center">
                <div className="h-3 w-3 bg-blue-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-600">
                  Transmission (42%)
                </span>
              </div>
              <div className="flex items-center">
                <div className="h-3 w-3 bg-purple-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-600">Engine (28%)</span>
              </div>
              <div className="flex items-center">
                <div className="h-3 w-3 bg-green-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-600">Diagnostics (18%)</span>
              </div>
              <div className="flex items-center">
                <div className="h-3 w-3 bg-yellow-500 rounded-full mr-2"></div>
                <span className="text-sm text-gray-600">Other (12%)</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <motion.div className="bg-white rounded-xl shadow-sm p-6" initial={{
        opacity: 0,
        y: 20
      }} animate={{
        opacity: 1,
        y: 0
      }} transition={{
        delay: 0.8
      }}>
          <h2 className="text-lg font-semibold text-gray-900 mb-6">
            System Notifications
          </h2>
          <div className="space-y-4">
            <div className="flex p-3 bg-yellow-50 border-l-4 border-yellow-500 rounded">
              <AlertCircleIcon className="h-5 w-5 text-yellow-500 mr-3 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-yellow-800">
                  Low inventory alert
                </p>
                <p className="text-xs text-yellow-600 mt-1">
                  Transmission fluid stock is running low
                </p>
              </div>
            </div>
            <div className="flex p-3 bg-green-50 border-l-4 border-green-500 rounded">
              <CheckCircleIcon className="h-5 w-5 text-green-500 mr-3 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-green-800">
                  Database backup complete
                </p>
                <p className="text-xs text-green-600 mt-1">
                  Last backup: Today at 3:45 AM
                </p>
              </div>
            </div>
            <div className="flex p-3 bg-blue-50 border-l-4 border-blue-500 rounded">
              <BellIcon className="h-5 w-5 text-blue-500 mr-3 flex-shrink-0" />
              <div>
                <p className="text-sm font-medium text-blue-800">
                  New car inquiry received
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  2019 Ford Mustang GT
                </p>
              </div>
            </div>
          </div>
        </motion.div>
        <motion.div className="lg:col-span-2 bg-gradient-to-r from-[#1e3a5f] to-blue-800 rounded-xl shadow-sm p-6 text-white" initial={{
        opacity: 0,
        y: 20
      }} animate={{
        opacity: 1,
        y: 0
      }} transition={{
        delay: 0.9
      }}>
          <h2 className="text-lg font-semibold mb-2">Quick Actions</h2>
          <p className="text-blue-100 mb-6">Frequently used admin functions</p>
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
            <button className="bg-white/10 hover:bg-white/20 p-4 rounded-xl transition-colors flex flex-col items-center">
              <CarIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Add New Car</span>
            </button>
            <button className="bg-white/10 hover:bg-white/20 p-4 rounded-xl transition-colors flex flex-col items-center">
              <ClipboardListIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">New Service</span>
            </button>
            <button className="bg-white/10 hover:bg-white/20 p-4 rounded-xl transition-colors flex flex-col items-center">
              <UsersIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Add Customer</span>
            </button>
            <button className="bg-white/10 hover:bg-white/20 p-4 rounded-xl transition-colors flex flex-col items-center">
              <FileTextIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Generate Report</span>
            </button>
            <button className="bg-white/10 hover:bg-white/20 p-4 rounded-xl transition-colors flex flex-col items-center">
              <CalendarIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Schedule</span>
            </button>
            <button className="bg-white/10 hover:bg-white/20 p-4 rounded-xl transition-colors flex flex-col items-center">
              <SettingsIcon className="h-6 w-6 mb-2" />
              <span className="text-sm">Settings</span>
            </button>
          </div>
        </motion.div>
      </div>
    </div>;
};
export default Dashboard;