import { useState } from 'react';
import {
  SearchIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  ArrowUpDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  FilterIcon,
  PhoneIcon,
  MailIcon,
  ArrowLeftIcon,
  CarIcon,
  ClipboardListIcon,
  FileTextIcon,
  CalendarIcon,
  MenuIcon,
  XIcon,
} from 'lucide-react';
import AdminSidebar from '../components/AdminSidebar';
const AdminCustomerDatabase = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState('name');
  const [sortDirection, setSortDirection] = useState('asc');
  const [selectedCustomer, setSelectedCustomer] = useState(null);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Sample customers data
  const customers = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '(*************',
      address: '123 Main St, Anytown, CA 12345',
      joinDate: '2021-05-12',
      vehicles: [
        {
          make: 'Toyota',
          model: 'Camry',
          year: 2018,
          vin: '4T1B11HK5JU123456',
        },
      ],
      serviceHistory: [
        {
          id: 'SR-2023-089',
          date: '2023-10-12',
          service: 'Transmission Rebuilding',
          status: 'In Progress',
          cost: '$3,800',
        },
        {
          id: 'SR-2022-456',
          date: '2022-07-23',
          service: 'Oil Change & Inspection',
          status: 'Completed',
          cost: '$95',
        },
      ],
      notes: 'Prefers to be contacted by email. Usually available after 5pm.',
    },
    {
      id: 2,
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '(*************',
      address: '456 Oak Ave, Somewhere, NY 67890',
      joinDate: '2022-02-18',
      vehicles: [
        {
          make: 'Ford',
          model: 'F-150',
          year: 2020,
          vin: '1FTEW1EP5LFA12345',
        },
      ],
      serviceHistory: [
        {
          id: 'SR-2023-088',
          date: '2023-10-11',
          service: 'Engine Diagnostics',
          status: 'Completed',
          cost: '$365',
        },
      ],
      notes: '',
    },
    {
      id: 3,
      name: 'David Chen',
      email: '<EMAIL>',
      phone: '(*************',
      address: '789 Pine Rd, Elsewhere, TX 54321',
      joinDate: '2020-11-30',
      vehicles: [
        {
          make: 'Mercedes',
          model: 'C300',
          year: 2019,
          vin: 'WDDWF4KB2KR123456',
        },
      ],
      serviceHistory: [
        {
          id: 'SR-2023-087',
          date: '2023-10-10',
          service: 'Brake Service',
          status: 'Scheduled',
          cost: '$650',
        },
        {
          id: 'SR-2023-045',
          date: '2023-05-15',
          service: 'Oil Change & Inspection',
          status: 'Completed',
          cost: '$95',
        },
      ],
      notes: 'Prefers text message reminders for appointments.',
    },
    {
      id: 4,
      name: 'Emily Wilson',
      email: '<EMAIL>',
      phone: '(*************',
      address: '321 Maple Dr, Nowhere, FL 98765',
      joinDate: '2022-08-05',
      vehicles: [
        {
          make: 'Lexus',
          model: 'RX350',
          year: 2017,
          vin: '2T2ZZMCA4HC123456',
        },
      ],
      serviceHistory: [
        {
          id: 'SR-2023-086',
          date: '2023-10-09',
          service: 'Key Programming',
          status: 'Pending',
          cost: '-',
        },
      ],
      notes: 'Referred by Sarah Johnson (Customer #2).',
    },
    {
      id: 5,
      name: 'Robert Garcia',
      email: '<EMAIL>',
      phone: '(*************',
      address: '654 Elm St, Anywhere, WA 24680',
      joinDate: '2021-12-15',
      vehicles: [
        {
          make: 'Chevrolet',
          model: 'Tahoe',
          year: 2021,
          vin: '1GNSKCKD1MR123456',
        },
        {
          make: 'Honda',
          model: 'Civic',
          year: 2018,
          vin: '19XFC2F53JE123456',
        },
      ],
      serviceHistory: [
        {
          id: 'SR-2023-085',
          date: '2023-10-08',
          service: 'Oil Change & Inspection',
          status: 'Completed',
          cost: '$95',
        },
        {
          id: 'SR-2023-062',
          date: '2023-07-14',
          service: 'A/C Repair',
          status: 'Completed',
          cost: '$850',
        },
      ],
      notes: 'Has multiple vehicles. Prefers to schedule service on Saturdays.',
    },
  ];

  // Filter customers based on search query
  const filteredCustomers = customers.filter((customer) => {
    return (
      customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      customer.phone.includes(searchQuery)
    );
  });

  // Sort customers
  const sortedCustomers = [...filteredCustomers].sort((a, b) => {
    if (sortField === 'name') {
      return sortDirection === 'asc'
        ? a.name.localeCompare(b.name)
        : b.name.localeCompare(a.name);
    }
    if (sortField === 'email') {
      return sortDirection === 'asc'
        ? a.email.localeCompare(b.email)
        : b.email.localeCompare(a.email);
    }
    if (sortField === 'joinDate') {
      return sortDirection === 'asc'
        ? new Date(a.joinDate).getTime() - new Date(b.joinDate).getTime()
        : new Date(b.joinDate).getTime() - new Date(a.joinDate).getTime();
    }
    return 0;
  });

  // Pagination
  const itemsPerPage = 10;
  const totalPages = Math.ceil(sortedCustomers.length / itemsPerPage);
  const paginatedCustomers = sortedCustomers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  const handleViewCustomer = (customer: any) => {
    setSelectedCustomer(customer);
  };

  if (selectedCustomer) {
    return (
      <div className="flex h-screen bg-gray-100">
        {/* Sidebar for desktop */}
        <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
          <AdminSidebar activePage="customers" />
        </aside>
        {/* Mobile sidebar */}
        <div className={`fixed inset-0 bg-gray-800 bg-opacity-75 z-40 md:hidden transition-opacity duration-200 ${sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`} onClick={toggleSidebar}></div>
        <aside className={`fixed top-0 left-0 h-full bg-[#1e3a5f] text-white w-64 z-50 transform transition-transform duration-200 ease-in-out md:hidden ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
          <div className="absolute top-4 right-4">
            <button className="text-white p-2" onClick={toggleSidebar}>
              <XIcon className="h-6 w-6" />
            </button>
          </div>
          <AdminSidebar activePage="customers" />
        </aside>
        {/* Main content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <CustomerDetail
            customer={selectedCustomer}
            onBack={() => setSelectedCustomer(null)}
          />
        </div>
      </div>
    );
  }
  return <div className="flex h-screen bg-gray-100">
      {/* Sidebar for desktop */}
      <aside className="bg-[#1e3a5f] text-white w-64 flex-shrink-0 hidden md:block">
        <AdminSidebar activePage="customers" />
      </aside>
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 bg-gray-800 bg-opacity-75 z-40 md:hidden transition-opacity duration-200 ${sidebarOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'}`} onClick={toggleSidebar}></div>
      <aside className={`fixed top-0 left-0 h-full bg-[#1e3a5f] text-white w-64 z-50 transform transition-transform duration-200 ease-in-out md:hidden ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="absolute top-4 right-4">
          <button className="text-white p-2" onClick={toggleSidebar}>
            <XIcon className="h-6 w-6" />
          </button>
        </div>
        <AdminSidebar activePage="customers" />
      </aside>
      {/* Main content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top header */}
        <header className="bg-white shadow-sm z-10">
          <div className="px-4 py-4 flex justify-between items-center">
            <div className="flex items-center md:hidden">
              <button className="text-gray-600 p-2" onClick={toggleSidebar}>
                <MenuIcon className="h-6 w-6" />
              </button>
            </div>
            <h1 className="text-xl font-semibold text-gray-800">
              Customer Database
            </h1>
            <button className="bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-2 px-4 rounded-lg transition-colors flex items-center">
              <PlusIcon className="h-5 w-5 mr-2" />
              Add Customer
            </button>
          </div>
        </header>
        {/* Main content area */}
        <main className="flex-1 overflow-y-auto bg-gray-100 p-4">
          <div className="space-y-6">
            {/* Search and Filter Section */}
            <div className="bg-white rounded-xl shadow-md p-4">
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search by name, email, or phone..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                    <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  </div>
                </div>
                <button className="p-2 text-gray-600 hover:text-gray-800">
                  <FilterIcon className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Customer Table */}
            <div className="bg-white rounded-xl shadow-md overflow-hidden">
              <div className="overflow-x-auto">
                <table className="min-w-full">
                  <thead>
                    <tr className="bg-gray-100 border-b border-gray-200">
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <button
                          onClick={() => handleSort('name')}
                          className="flex items-center"
                        >
                          Name
                          <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                        </button>
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <button
                          onClick={() => handleSort('email')}
                          className="flex items-center"
                        >
                          Email
                          <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                        </button>
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Phone
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Vehicles
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        <button
                          onClick={() => handleSort('joinDate')}
                          className="flex items-center"
                        >
                          Customer Since
                          <ArrowUpDownIcon className="h-4 w-4 ml-1" />
                        </button>
                      </th>
                      <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {paginatedCustomers.map((customer) => (
                      <tr key={customer.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {customer.name}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {customer.email}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {customer.phone}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                          {customer.vehicles.length === 1
                            ? `${customer.vehicles[0].year} ${customer.vehicles[0].make} ${customer.vehicles[0].model}`
                            : `${customer.vehicles.length} vehicles`}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                          {customer.joinDate}
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => handleViewCustomer(customer)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <EyeIcon className="h-5 w-5" />
                            </button>
                            <button className="text-blue-600 hover:text-blue-900">
                              <PencilIcon className="h-5 w-5" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="px-4 py-3 flex items-center justify-between border-t border-gray-200">
                  <div className="flex-1 flex justify-between items-center">
                    <p className="text-sm text-gray-700">
                      Showing{' '}
                      <span className="font-medium">
                        {(currentPage - 1) * itemsPerPage + 1}
                      </span>{' '}
                      to{' '}
                      <span className="font-medium">
                        {Math.min(currentPage * itemsPerPage, sortedCustomers.length)}
                      </span>{' '}
                      of <span className="font-medium">{sortedCustomers.length}</span>{' '}
                      results
                    </p>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setCurrentPage(currentPage - 1)}
                        disabled={currentPage === 1}
                        className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white ${
                          currentPage === 1 
                            ? 'text-gray-300' 
                            : 'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <ChevronLeftIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => setCurrentPage(currentPage + 1)}
                        disabled={currentPage === totalPages}
                        className={`relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white ${
                          currentPage === totalPages 
                            ? 'text-gray-300' 
                            : 'text-gray-700 hover:bg-gray-50'
                        }`}
                      >
                        <ChevronRightIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>;
};

const CustomerDetail = ({ customer, onBack }: { customer: any; onBack: () => void }) => {
  const [activeTab, setActiveTab] = useState('profile');

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'in progress':
        return 'bg-blue-100 text-blue-800';
      case 'scheduled':
        return 'bg-purple-100 text-purple-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6">
      <button
        onClick={onBack}
        className="flex items-center text-blue-600 hover:text-blue-800 mb-6"
      >
        <ArrowLeftIcon className="h-4 w-4 mr-2" />
        Back to Customer Database
      </button>

      <div className="bg-white rounded-xl shadow-md overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">
                {customer.name}
              </h2>
              <p className="text-gray-600">
                Customer since {customer.joinDate}
              </p>
            </div>
            <div className="mt-4 md:mt-0 flex space-x-2">
              <a
                href={`tel:${customer.phone}`}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                <PhoneIcon className="h-5 w-5 mr-2" />
                Call
              </a>
              <a
                href={`mailto:${customer.email}`}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
              >
                <MailIcon className="h-5 w-5 mr-2" />
                Email
              </a>
              <button className="inline-flex items-center px-4 py-2 bg-[#1e3a5f] hover:bg-blue-800 text-white rounded-lg transition-colors">
                <PlusIcon className="h-5 w-5 mr-2" />
                New Service
              </button>
            </div>
          </div>
        </div>

        <div className="border-b border-gray-200">
          <nav className="flex">
            <button
              onClick={() => setActiveTab('profile')}
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === 'profile'
                  ? 'border-b-2 border-blue-600 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Profile
            </button>
            <button
              onClick={() => setActiveTab('vehicles')}
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === 'vehicles'
                  ? 'border-b-2 border-blue-600 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Vehicles
            </button>
            <button
              onClick={() => setActiveTab('service')}
              className={`px-6 py-3 text-sm font-medium ${
                activeTab === 'service'
                  ? 'border-b-2 border-blue-600 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Service History
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'profile' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Contact Information
                </h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="space-y-4">
                    <div>
                      <p className="text-gray-500 text-sm">Full Name</p>
                      <p className="font-medium">{customer.name}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Email</p>
                      <div className="flex items-center">
                        <MailIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <a
                          href={`mailto:${customer.email}`}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          {customer.email}
                        </a>
                      </div>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Phone</p>
                      <div className="flex items-center">
                        <PhoneIcon className="h-4 w-4 text-gray-400 mr-2" />
                        <a
                          href={`tel:${customer.phone}`}
                          className="text-blue-600 hover:text-blue-800"
                        >
                          {customer.phone}
                        </a>
                      </div>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm">Address</p>
                      <p>{customer.address}</p>
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Notes
                </h3>
                <div className="bg-gray-50 rounded-lg p-4 h-full">
                  <textarea
                    className="w-full h-40 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    defaultValue={customer.notes}
                    placeholder="Add notes about this customer..."
                  ></textarea>
                  <div className="flex justify-end mt-3">
                    <button className="px-4 py-2 bg-[#1e3a5f] hover:bg-blue-800 text-white rounded-lg transition-colors">
                      Save Notes
                    </button>
                  </div>
                </div>
              </div>
              <div className="md:col-span-2">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Recent Activity
                </h3>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="space-y-4">
                    {customer.serviceHistory
                      .slice(0, 3)
                      .map((service: any, index: number) => (
                        <div key={index} className="flex items-start">
                          <div className="bg-blue-100 p-2 rounded-full mr-3">
                            <ClipboardListIcon className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium">{service.service}</p>
                            <div className="flex items-center text-sm text-gray-500">
                              <CalendarIcon className="h-4 w-4 mr-1" />
                              {service.date}
                              <span
                                className={`ml-2 px-2 py-0.5 rounded-full text-xs ${getStatusColor(service.status)}`}
                              >
                                {service.status}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'vehicles' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  Customer Vehicles
                </h3>
                <button className="inline-flex items-center px-4 py-2 bg-[#1e3a5f] hover:bg-blue-800 text-white rounded-lg transition-colors">
                  <PlusIcon className="h-5 w-5 mr-2" />
                  Add Vehicle
                </button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {customer.vehicles.map((vehicle: any, index: number) => (
                  <div
                    key={index}
                    className="bg-gray-50 rounded-lg p-6 border border-gray-200"
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="text-xl font-semibold text-gray-900">
                          {vehicle.year} {vehicle.make} {vehicle.model}
                        </h4>
                        <p className="text-gray-500">VIN: {vehicle.vin}</p>
                      </div>
                      <div className="bg-blue-100 p-2 rounded-full">
                        <CarIcon className="h-6 w-6 text-blue-600" />
                      </div>
                    </div>
                    <div className="mt-6 pt-6 border-t border-gray-200">
                      <div className="flex justify-between">
                        <button className="text-blue-600 hover:text-blue-800 font-medium">
                          View Service History
                        </button>
                        <button className="text-blue-600 hover:text-blue-800 font-medium">
                          Edit Details
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'service' && (
            <div>
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-gray-900">
                  Service History
                </h3>
                <button className="inline-flex items-center px-4 py-2 bg-[#1e3a5f] hover:bg-blue-800 text-white rounded-lg transition-colors">
                  <FileTextIcon className="h-5 w-5 mr-2" />
                  Generate Report
                </button>
              </div>
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        ID
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Service
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Cost
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {customer.serviceHistory.map((service: any, index: number) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600">
                          {service.id}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {service.date}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {service.service}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusColor(service.status)}`}
                          >
                            {service.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {service.cost}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button className="text-blue-600 hover:text-blue-800">
                            View Details
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdminCustomerDatabase;