import React, { useEffect, useState, useRef, Children } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import { Link } from 'react-router-dom';
import { SearchIcon, SlidersIcon, CheckIcon, ChevronDownIcon, StarIcon } from 'lucide-react';
const CarsForSale = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [sortOption, setSortOption] = useState('newest');
  const [filterMenuOpen, setFilterMenuOpen] = useState(false);
  const ref = useRef(null);
  const isInView = useInView(ref, {
    once: true
  });
  const controls = useAnimation();
  useEffect(() => {
    if (isInView) {
      controls.start('visible');
    }
  }, [isInView, controls]);
  const containerVariants = {
    hidden: {
      opacity: 0
    },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };
  const itemVariants = {
    hidden: {
      y: 20,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        ease: 'easeOut'
      }
    }
  };
  // Sample car data
  const cars = [{
    id: 1,
    title: '2019 Ford Mustang GT',
    price: '$32,500',
    image: 'https://images.unsplash.com/photo-1581650107963-3e8c1f0f0783?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '45,000 miles',
    year: 2019,
    make: 'Ford',
    model: 'Mustang GT',
    engine: '5.0L V8',
    transmission: '6-Speed Manual',
    exteriorColor: 'Race Red',
    interiorColor: 'Black',
    featured: true,
    category: 'sports'
  }, {
    id: 2,
    title: '2018 Toyota Camry XSE',
    price: '$22,800',
    image: 'https://images.unsplash.com/photo-1621007947382-bb3c3994e3fb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '38,500 miles',
    year: 2018,
    make: 'Toyota',
    model: 'Camry XSE',
    engine: '2.5L 4-Cylinder',
    transmission: '8-Speed Automatic',
    exteriorColor: 'Midnight Black',
    interiorColor: 'Red',
    featured: false,
    category: 'sedan'
  }, {
    id: 3,
    title: '2020 Chevrolet Tahoe',
    price: '$42,999',
    image: 'https://images.unsplash.com/photo-1533473359331-0135ef1b58bf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '29,000 miles',
    year: 2020,
    make: 'Chevrolet',
    model: 'Tahoe',
    engine: '5.3L V8',
    transmission: '10-Speed Automatic',
    exteriorColor: 'Summit White',
    interiorColor: 'Jet Black',
    featured: true,
    category: 'suv'
  }, {
    id: 4,
    title: '2021 Honda Accord Sport',
    price: '$28,750',
    image: 'https://images.unsplash.com/photo-1583121274602-3e2820c69888?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '12,300 miles',
    year: 2021,
    make: 'Honda',
    model: 'Accord Sport',
    engine: '1.5L Turbo 4-Cylinder',
    transmission: 'CVT',
    exteriorColor: 'Crystal Black Pearl',
    interiorColor: 'Black',
    featured: false,
    category: 'sedan'
  }, {
    id: 5,
    title: '2017 Jeep Wrangler Unlimited',
    price: '$31,990',
    image: 'https://images.unsplash.com/photo-1563720223523-491ff04651de?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
    mileage: '52,000 miles',
    year: 2017,
    make: 'Jeep',
    model: 'Wrangler Unlimited',
    engine: '3.6L V6',
    transmission: '6-Speed Manual',
    exteriorColor: 'Firecracker Red',
    interiorColor: 'Black',
    featured: false,
    category: 'suv'
  }, {
    id: 6,
    title: '2022 BMW 330i xDrive',
    price: '$45,900',
    image: 'https://images.unsplash.com/photo-1580273916550-e323be2ae537?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1460&q=80',
    mileage: '8,700 miles',
    year: 2022,
    make: 'BMW',
    model: '330i xDrive',
    engine: '2.0L Turbo 4-Cylinder',
    transmission: '8-Speed Automatic',
    exteriorColor: 'Alpine White',
    interiorColor: 'Cognac',
    featured: true,
    category: 'luxury'
  }];
  // Filter cars based on active filter
  const filteredCars = activeFilter === 'all' ? cars : activeFilter === 'featured' ? cars.filter(car => car.featured) : cars.filter(car => car.category === activeFilter);
  // Sort cars based on sort option
  const sortedCars = [...filteredCars].sort((a, b) => {
    if (sortOption === 'newest') return b.year - a.year;
    if (sortOption === 'oldest') return a.year - b.year;
    if (sortOption === 'price-low') return parseInt(a.price.replace(/\D/g, '')) - parseInt(b.price.replace(/\D/g, ''));
    if (sortOption === 'price-high') return parseInt(b.price.replace(/\D/g, '')) - parseInt(a.price.replace(/\D/g, ''));
    return 0;
  });
  const filterOptions = [{
    id: 'all',
    label: 'All Vehicles'
  }, {
    id: 'featured',
    label: 'Featured'
  }, {
    id: 'sedan',
    label: 'Sedans'
  }, {
    id: 'suv',
    label: 'SUVs & Crossovers'
  }, {
    id: 'sports',
    label: 'Sports Cars'
  }, {
    id: 'luxury',
    label: 'Luxury Vehicles'
  }];
  const sortOptions = [{
    id: 'newest',
    label: 'Newest First'
  }, {
    id: 'oldest',
    label: 'Oldest First'
  }, {
    id: 'price-low',
    label: 'Price: Low to High'
  }, {
    id: 'price-high',
    label: 'Price: High to Low'
  }];
  return <main className="w-full pt-20">
      {/* Hero Section */}
      <section className="relative bg-[#1e3a5f] py-16">
        <div className="absolute inset-0 z-0 opacity-20">
          <div className="w-full h-full bg-cover bg-center" style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80')"
        }}></div>
        </div>
        <div className="container mx-auto px-4 md:px-8 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
              Find Your Perfect Vehicle
            </h1>
            <p className="text-lg text-blue-100 mb-8">
              Browse our selection of quality pre-owned vehicles at competitive
              prices.
            </p>
            <div className="bg-white p-4 rounded-xl shadow-lg">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-grow relative">
                  <input type="text" placeholder="Search by make, model, or keyword..." className="w-full pl-10 pr-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent" />
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                </div>
                <button className="bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-3 px-6 rounded-lg transition-colors">
                  Search Inventory
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Inventory Section */}
      <section ref={ref} className="py-16 bg-slate-100">
        <div className="container mx-auto px-4 md:px-8">
          <motion.div variants={containerVariants} initial="hidden" animate={controls} className="flex flex-col lg:flex-row gap-8">
            {/* Sidebar Filters - Desktop */}
            <motion.div variants={itemVariants} className="lg:w-1/4 hidden lg:block">
              <div className="bg-white rounded-xl shadow-md p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  Filter Vehicles
                </h3>
                <div className="space-y-4">
                  {filterOptions.map(option => <button key={option.id} onClick={() => setActiveFilter(option.id)} className={`flex items-center w-full px-3 py-2 rounded-lg transition-colors ${activeFilter === option.id ? 'bg-blue-100 text-[#1e3a5f]' : 'text-gray-700 hover:bg-gray-100'}`}>
                      {activeFilter === option.id && <CheckIcon className="h-4 w-4 mr-2 text-blue-600" />}
                      <span>{option.label}</span>
                      {option.id === 'featured' && <span className="ml-auto bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
                          {cars.filter(car => car.featured).length}
                        </span>}
                    </button>)}
                </div>
                <div className="border-t border-gray-200 my-6 pt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Price Range
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 text-sm">$0</span>
                      <span className="text-gray-600 text-sm">$50,000+</span>
                    </div>
                    <input type="range" min="0" max="50000" step="1000" defaultValue="50000" className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer" />
                  </div>
                </div>
                <div className="border-t border-gray-200 my-6 pt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Year
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                      <option>From</option>
                      {[...Array(10)].map((_, i) => <option key={i} value={2013 + i}>
                          {2013 + i}
                        </option>)}
                    </select>
                    <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                      <option>To</option>
                      {[...Array(10)].map((_, i) => <option key={i} value={2023 - i}>
                          {2023 - i}
                        </option>)}
                    </select>
                  </div>
                </div>
                <div className="border-t border-gray-200 my-6 pt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Mileage
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600 text-sm">0 miles</span>
                      <span className="text-gray-600 text-sm">
                        100,000+ miles
                      </span>
                    </div>
                    <input type="range" min="0" max="100000" step="5000" defaultValue="100000" className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer" />
                  </div>
                </div>
                <div className="mt-6">
                  <button className="w-full bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-3 rounded-lg transition-colors">
                    Apply Filters
                  </button>
                  <button className="w-full text-gray-600 hover:text-[#1e3a5f] font-medium py-2 mt-2 transition-colors">
                    Reset All Filters
                  </button>
                </div>
              </div>
            </motion.div>

            {/* Mobile Filters Button */}
            <motion.div variants={itemVariants} className="lg:hidden">
              <button onClick={() => setFilterMenuOpen(!filterMenuOpen)} className="w-full flex items-center justify-center gap-2 bg-white py-3 px-4 rounded-lg shadow-md text-gray-700 font-medium">
                <SlidersIcon className="h-5 w-5" />
                Filter Vehicles
                <ChevronDownIcon className={`h-5 w-5 transition-transform ${filterMenuOpen ? 'rotate-180' : ''}`} />
              </button>
              {filterMenuOpen && <div className="mt-3 bg-white rounded-xl shadow-md p-6">
                  <div className="flex flex-wrap gap-2 mb-4">
                    {filterOptions.map(option => <button key={option.id} onClick={() => {
                  setActiveFilter(option.id);
                  setFilterMenuOpen(false);
                }} className={`px-4 py-2 rounded-full text-sm font-medium ${activeFilter === option.id ? 'bg-[#1e3a5f] text-white' : 'bg-gray-100 text-gray-700'}`}>
                        {option.label}
                      </button>)}
                  </div>
                  <button onClick={() => setFilterMenuOpen(false)} className="w-full bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-2 rounded-lg transition-colors">
                    Apply
                  </button>
                </div>}
            </motion.div>

            {/* Car Listings */}
            <motion.div variants={itemVariants} className="lg:w-3/4">
              <div className="bg-white rounded-xl shadow-md p-6 mb-6">
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">
                      Available Vehicles
                    </h2>
                    <p className="text-gray-600">
                      {sortedCars.length} vehicles found
                    </p>
                  </div>
                  <div className="flex items-center">
                    <span className="text-gray-600 mr-2">Sort by:</span>
                    <select value={sortOption} onChange={e => setSortOption(e.target.value)} className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                      {sortOptions.map(option => <option key={option.id} value={option.id}>
                          {option.label}
                        </option>)}
                    </select>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                {sortedCars.map((car, index) => <CarCard key={car.id} car={car} index={index} />)}
              </div>
              {sortedCars.length === 0 && <div className="bg-white rounded-xl shadow-md p-8 text-center">
                  <p className="text-gray-600 mb-4">
                    No vehicles found matching your criteria.
                  </p>
                  <button onClick={() => setActiveFilter('all')} className="text-[#1e3a5f] font-medium hover:underline">
                    Clear filters and view all vehicles
                  </button>
                </div>}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-b from-[#1e3a5f] to-[#0f2542] text-white">
        <div className="container mx-auto px-4 md:px-8 text-center">
          <h2 className="text-3xl font-bold mb-6">
            Don't See What You're Looking For?
          </h2>
          <p className="text-blue-100 max-w-2xl mx-auto mb-8">
            Our inventory changes frequently. Let us know what you're looking
            for and we'll help you find the perfect vehicle.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link to="/contact" className="bg-white hover:bg-gray-100 text-[#1e3a5f] px-8 py-3 rounded-full font-semibold text-lg">
              Contact Us
            </Link>
            <a href="tel:+16714838335" className="border-2 border-white text-white hover:bg-white hover:text-[#1e3a5f] px-8 py-3 rounded-full font-semibold text-lg inline-flex items-center transition-colors">
              Call (*************
            </a>
          </div>
        </div>
      </section>
    </main>;
};
const CarCard = ({
  car,
  index
}) => {
  const cardVariants = {
    hidden: {
      y: 20,
      opacity: 0
    },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
        delay: index * 0.1,
        ease: 'easeOut'
      }
    }
  };
  return <motion.div initial={{
    y: 20,
    opacity: 0
  }} animate={{
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5,
      delay: index * 0.1,
      ease: 'easeOut'
    }
  }} whileHover={{
    y: -10,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 25
    }
  }} style={{
    transform: 'translateY(0px)'
  }} className="bg-white rounded-2xl overflow-hidden shadow-xl relative">
      {car.featured && <div className="absolute top-4 left-4 z-10">
          <span className="bg-yellow-500 text-white text-sm font-bold px-3 py-1 rounded-full">
            Featured
          </span>
        </div>}
      <div className="h-56 overflow-hidden">
        <img src={car.image} alt={car.title} className="w-full h-full object-cover transition-transform duration-500 hover:scale-110" />
      </div>
      <div className="p-6">
        <div className="flex justify-between items-center mb-2">
          <h3 className="text-xl font-bold text-gray-900">{car.title}</h3>
          <div className="flex">
            <StarIcon className="h-5 w-5 text-yellow-500" />
            <StarIcon className="h-5 w-5 text-yellow-500" />
            <StarIcon className="h-5 w-5 text-yellow-500" />
            <StarIcon className="h-5 w-5 text-yellow-500" />
            <StarIcon className="h-5 w-5 text-yellow-500" />
          </div>
        </div>
        <span className="text-xl font-bold text-blue-700 block mb-3">
          {car.price}
        </span>
        <div className="grid grid-cols-2 gap-y-2 text-sm text-gray-600 mb-6">
          <div>
            <span className="font-medium">Year:</span> {car.year}
          </div>
          <div>
            <span className="font-medium">Mileage:</span> {car.mileage}
          </div>
          <div>
            <span className="font-medium">Engine:</span> {car.engine}
          </div>
          <div>
            <span className="font-medium">Transmission:</span>{' '}
            {car.transmission}
          </div>
        </div>
        <Link to={`/cars/${car.id}`} className="inline-block bg-[#1e3a5f] hover:bg-blue-800 text-white font-semibold py-2 px-6 rounded-lg transition-colors w-full text-center">
          View Details
        </Link>
      </div>
    </motion.div>;
};
export default CarsForSale;