import React from 'react';
import { motion } from 'framer-motion';
import { LayoutDashboardIcon, CarIcon, ClipboardListIcon, UsersIcon, SettingsIcon, LogOutIcon, FileTextIcon, ExternalLinkIcon, MessageSquareIcon } from 'lucide-react';
import { Link, useNavigate } from 'react-router-dom';
const AdminSidebar = ({
  activePage = 'dashboard'
}) => {
  const navigate = useNavigate();

  const handleLogout = () => {
    localStorage.removeItem('adminAuth');
    navigate('/admin/login');
  };

  return <div className="h-full flex flex-col">
      <div className="p-5 border-b border-blue-800">
        <div className="flex items-center">
          <WrenchAnimation />
          <span className="text-xl font-bold ml-2">ADJ Automotive</span>
        </div>
        <div className="text-blue-200 text-sm mt-1">Admin Portal</div>
      </div>
      <nav className="flex-1 px-2 py-4 space-y-1">
        <SidebarLink to="/admin" icon={<LayoutDashboardIcon className="h-5 w-5" />} text="Dashboard" active={activePage === 'dashboard'} />
        <SidebarLink to="/admin/cars" icon={<CarIcon className="h-5 w-5" />} text="Car Inventory" active={activePage === 'cars'} />
        <SidebarLink to="/admin/car-inquiries" icon={<MessageSquareIcon className="h-5 w-5" />} text="Car Inquiries" active={activePage === 'car-inquiries'} />
        <SidebarLink to="/admin/service-requests" icon={<ClipboardListIcon className="h-5 w-5" />} text="Service Requests" active={activePage === 'service-requests'} />
        <SidebarLink to="/admin/customers" icon={<UsersIcon className="h-5 w-5" />} text="Customer Database" active={activePage === 'customers'} />
        <SidebarLink to="/admin/reports" icon={<FileTextIcon className="h-5 w-5" />} text="Reports" active={activePage === 'reports'} />
        <SidebarLink to="/admin/settings" icon={<SettingsIcon className="h-5 w-5" />} text="Settings" active={activePage === 'settings'} />
      </nav>
      <div className="p-4 border-t border-blue-800 space-y-3">
        <a
          href="/"
          target="_blank"
          rel="noopener noreferrer"
          className="flex items-center px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-800 transition-colors w-full rounded-lg"
        >
          <ExternalLinkIcon className="h-5 w-5 mr-3" />
          <span className="font-medium">View Website</span>
        </a>
        <button 
          onClick={handleLogout}
          className="flex items-center px-4 py-3 text-blue-200 hover:text-white hover:bg-blue-800 transition-colors w-full rounded-lg"
        >
          <LogOutIcon className="h-5 w-5 mr-3" />
          <span className="font-medium">Sign Out</span>
        </button>
      </div>
    </div>;
};
interface SidebarLinkProps {
  to: string;
  icon: React.ReactNode;
  text: string;
  active?: boolean;
}

const SidebarLink = ({
  to,
  icon,
  text,
  active = false
}: SidebarLinkProps) => {
  return <Link to={to} className={`flex items-center px-4 py-3 rounded-lg transition-colors ${active ? 'bg-blue-700 text-white' : 'text-blue-200 hover:bg-blue-800 hover:text-white'}`}>
      <div className="mr-3">{icon}</div>
      <span className="font-medium">{text}</span>
    </Link>;
};
const WrenchAnimation = () => {
  return <motion.div animate={{
    rotate: [0, -45, 0]
  }} transition={{
    duration: 1.5,
    repeat: Infinity,
    repeatDelay: 5
  }} className="h-8 w-8 bg-blue-500 rounded-lg flex items-center justify-center">
      <span className="text-lg font-bold">🔧</span>
    </motion.div>;
};
export default AdminSidebar;